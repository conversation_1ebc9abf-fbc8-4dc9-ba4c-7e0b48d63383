import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { supabase } from '@/lib/supabase'
import { Loader2, Lock, Eye, EyeOff, AlertCircle } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useLocation } from 'react-router-dom'

interface ResetPasswordProps {
  onNavigateToQuiz: () => void
  onNavigateToDashboard: () => void
}

export const ResetPassword: React.FC<ResetPasswordProps> = ({ 
  onNavigateToQuiz, 
  onNavigateToDashboard 
}) => {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [accessToken, setAccessToken] = useState<string | null>(null)
  const [refreshToken, setRefreshToken] = useState<string | null>(null)
  const [hashChecked, setHashChecked] = useState(false)
  const { refreshAuthState } = useAuth()
  const location = useLocation()

  // Extract access_token and refresh_token from URL hash or query params
  useEffect(() => {
    const checkForResetParams = async () => {
      try {
        console.log('ResetPassword: Checking for auth params');
        
        // Get hash and search params
        const hash = location.hash
        const searchParams = new URLSearchParams(location.search)
        
        // Check if Supabase already has a session (user might already be authenticated)
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          console.log('ResetPassword: User already has an active session', session.user.email);
          setHashChecked(true);
          return;
        }
        
        if (hash) {
          // Try to get tokens from hash
          const params = new URLSearchParams(hash.substring(1));
          const accessToken = params.get('access_token');
          const refreshToken = params.get('refresh_token');
          
          if (accessToken && refreshToken) {
            console.log('ResetPassword: Found tokens in hash');
            setAccessToken(accessToken);
            setRefreshToken(refreshToken);
            
            // Set the session with tokens from the URL hash
            const { data, error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });
            
            if (error) {
              console.error('Error setting session from hash:', error);
              setError('Failed to authenticate with the provided link. Please request a new password reset.');
            } else {
              console.log('Session set successfully from hash for:', data.user?.email);
              setHashChecked(true);
            }
            return;
          }
        }
        
        // If we get here, check if we have an auth flow in progress
        // This handles the case where Supabase redirects back with auth code
        const type = searchParams.get('type')
        if (type === 'recovery' || searchParams.get('code')) {
          console.log('ResetPassword: Auth code flow detected');
          
          // The Supabase SDK should automatically handle the code exchange
          // Just check if we now have a session
          const { data: { session: newSession } } = await supabase.auth.getSession();
          
          if (newSession) {
            console.log('ResetPassword: Session established after code exchange:', newSession.user.email);
            setHashChecked(true);
          } else {
            // Try to refresh auth state
            await refreshAuthState();
            const { data: { session: refreshedSession } } = await supabase.auth.getSession();
            
            if (refreshedSession) {
              console.log('ResetPassword: Session established after refresh:', refreshedSession.user.email);
              setHashChecked(true);
            } else {
              console.error('No session after code exchange and refresh');
              setError('Unable to authenticate with the provided link. Please request a new password reset.');
            }
          }
          return;
        }
        
        // If we get here, we could not find any valid auth info
        console.error('No valid auth information found in URL');
        setError('Invalid reset link. Please request a new password reset.');
        
      } catch (err) {
        console.error('Error checking auth params:', err);
        setError('An error occurred while processing your reset link. Please try again or request a new password reset.');
      }
    };
    
    checkForResetParams();
  }, [location.hash, location.search, refreshAuthState]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!password || !confirmPassword) {
      setError('Please fill in all fields')
      return
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long')
      return
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Verify we have an active session before updating
      const { data: sessionData } = await supabase.auth.getSession()
      console.log('Current session before update:', sessionData?.session ? 'Active' : 'None')
      
      if (!sessionData?.session) {
        // If no session, try to get it from the URL again
        const hash = window.location.hash
        if (hash) {
          const params = new URLSearchParams(hash.substring(1))
          const accessToken = params.get('access_token')
          const refreshToken = params.get('refresh_token')
          
          if (accessToken && refreshToken) {
            console.log('Resetting session with tokens from URL')
            const { error: sessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            })
            
            if (sessionError) {
              throw new Error('Failed to set authentication session: ' + sessionError.message)
            }
          } else {
            throw new Error('Missing authentication tokens in URL')
          }
        } else {
          throw new Error('No authentication session found and no tokens in URL')
        }
      }
      
      // Now try to update the password
      console.log('Attempting to update password')
      const { error: updateError } = await supabase.auth.updateUser({
        password: password
      })

      if (updateError) {
        throw updateError
      }

      console.log('Password updated successfully')
      
      // Refresh auth state to ensure we have the latest user data
      await refreshAuthState()
      
      setSuccess(true)
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        onNavigateToDashboard()
      }, 3000)

    } catch (error: any) {
      console.error('Error updating password:', error)
      setError(error.message || 'Failed to update password. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-white border-emerald-200">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mb-4">
              <Lock className="w-6 h-6 text-emerald-600" />
            </div>
            <CardTitle className="text-2xl font-semibold text-slate-800">
              Password Updated Successfully!
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-slate-600 mb-4">
              Your password has been updated successfully. You will be redirected to your dashboard shortly.
            </p>
            <div className="flex items-center justify-center space-x-2 text-emerald-600">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Redirecting...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Debug function to check session status
  const checkSessionStatus = async () => {
    try {
      const { data } = await supabase.auth.getSession()
      if (data?.session) {
        alert(`Active session for: ${data.session.user.email || 'unknown email'}`)
      } else {
        alert('No active session found')
      }
    } catch (err) {
      alert('Error checking session: ' + (err instanceof Error ? err.message : String(err)))
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-white border-emerald-200">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mb-4">
            <Lock className="w-6 h-6 text-emerald-600" />
          </div>
          <CardTitle className="text-2xl font-semibold text-slate-800">
            Reset Your Password
          </CardTitle>
          <p className="text-slate-600 mt-2">
            Enter your new password below
          </p>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-red-700 text-sm font-medium">Error</p>
                  <p className="text-red-600 text-sm">{error}</p>
                  <p className="text-red-500 text-xs mt-1">
                    Try clicking the link in your email again, or request a new password reset email.
                  </p>
                </div>
              </div>
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-slate-700 mb-2">
                New Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter new password"
                  className="w-full px-3 py-3 pr-10 border border-emerald-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-slate-700 mb-2">
                Confirm New Password
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm new password"
                  className="w-full px-3 py-3 pr-10 border border-emerald-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600"
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            )}

            <Button
              type="submit"
              disabled={isLoading || !password || !confirmPassword}
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white disabled:opacity-50 disabled:cursor-not-allowed py-3"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Updating Password...
                </>
              ) : (
                'Update Password'
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <button
              onClick={onNavigateToQuiz}
              className="text-emerald-600 hover:text-emerald-700 text-sm font-medium"
            >
              Back to Quiz
            </button>
          </div>
          
          {/* Debug buttons - only shown in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 text-center space-y-2 border-t border-gray-200 pt-4">
              <p className="text-sm font-semibold text-gray-500">Debug Tools</p>
              
              <button
                onClick={checkSessionStatus}
                className="text-blue-600 hover:text-blue-700 text-xs underline"
              >
                Check Session Status
              </button>
              
              <div className="text-xs text-left bg-gray-50 p-2 rounded-md">
                <p className="font-semibold">Auth Info:</p>
                <p>Hash: {location.hash || window.location.hash || 'none'}</p>
                <p>Search Params: {location.search || window.location.search || 'none'}</p>
                <p>Hash Checked: {hashChecked ? 'Yes' : 'No'}</p>
                <p>Access Token: {accessToken ? `${accessToken.substring(0, 10)}...` : 'none'}</p>
                <p>Refresh Token: {refreshToken ? `${refreshToken.substring(0, 10)}...` : 'none'}</p>
              </div>
              
              <button
                onClick={async () => {
                  try {
                    const { data: { session } } = await supabase.auth.getSession();
                    if (session) {
                      alert(`Current Session Info:\nUser: ${session.user.email}\nAuth Provider: ${session.user.app_metadata.provider || 'unknown'}\nExpires: ${new Date(session.expires_at! * 1000).toLocaleString()}`);
                    } else {
                      alert('No active session found');
                    }
                  } catch (err) {
                    alert('Error: ' + (err instanceof Error ? err.message : String(err)));
                  }
                }}
                className="text-purple-600 hover:text-purple-700 text-xs underline"
              >
                Detailed Session Info
              </button>
              
              <button
                onClick={async () => {
                  try {
                    await refreshAuthState();
                    const { data: { session } } = await supabase.auth.getSession();
                    if (session) {
                      alert(`Auth refreshed. Session active for: ${session.user.email}`);
                    } else {
                      alert('Auth refreshed, but no session found');
                    }
                  } catch (err) {
                    alert('Error: ' + (err instanceof Error ? err.message : String(err)));
                  }
                }}
                className="text-green-600 hover:text-green-700 text-xs underline"
              >
                Manual Auth Refresh
              </button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
