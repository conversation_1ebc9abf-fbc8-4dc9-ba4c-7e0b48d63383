import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'

interface Subscription {
  id: string
  stripe_id: string
  status: 'active' | 'canceled' | 'past_due'
  price: number
  period_end: string
  created_at: string
}

export function useSubscription() {
  const { user } = useAuth()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadSubscriptions = async () => {
    if (!user) {
      setSubscriptions([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const { data, error: fetchError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (fetchError) {
        throw fetchError
      }

      setSubscriptions(data || [])
    } catch (err) {
      console.error('Error loading subscriptions:', err)
      setError(err instanceof Error ? err.message : 'Failed to load subscriptions')
    } finally {
      setLoading(false)
    }
  }

  const cancelSubscription = async (stripeSubscriptionId: string) => {
    try {
      setError(null)

      // Call your backend API to cancel the subscription via Stripe
      const response = await fetch('/api/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ subscriptionId: stripeSubscriptionId }),
      })

      if (!response.ok) {
        throw new Error('Failed to cancel subscription')
      }

      // Reload subscriptions to get updated status
      await loadSubscriptions()
      return { success: true }
    } catch (err) {
      console.error('Error canceling subscription:', err)
      setError(err instanceof Error ? err.message : 'Failed to cancel subscription')
      return { success: false, error: err instanceof Error ? err.message : 'Failed to cancel subscription' }
    }
  }

  const getActiveSubscription = () => {
    return subscriptions.find(sub => sub.status === 'active') || null
  }

  const hasActiveSubscription = () => {
    return getActiveSubscription() !== null
  }

  useEffect(() => {
    loadSubscriptions()
  }, [user])

  return {
    subscriptions,
    loading,
    error,
    activeSubscription: getActiveSubscription(),
    hasActiveSubscription: hasActiveSubscription(),
    cancelSubscription,
    refreshSubscriptions: loadSubscriptions
  }
}