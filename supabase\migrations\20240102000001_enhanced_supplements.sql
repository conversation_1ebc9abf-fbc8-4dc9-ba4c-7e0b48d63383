-- ============================================================================
-- Enhanced Supplements Schema - Support for comprehensive supplement data
-- ============================================================================

-- Drop existing recommendations table to recreate with new structure
DROP TABLE IF EXISTS recommendations CASCADE;

-- Create enhanced recommendations table with comprehensive supplement data
CREATE TABLE supplements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    type supplement_type DEFAULT 'supplement',
    description TEXT,
    benefits TEXT[],
    side_effects TEXT[],
    contraindications TEXT[],
    dosage_info JSONB, -- {min_dose, max_dose, timing, form, with_food}
    interactions TEXT[],
    pregnancy_safe BOOLEAN DEFAULT false,
    breastfeeding_safe BOOLEAN DEFAULT false,
    age_restrictions JSONB, -- {min_age, max_age, elderly_considerations}
    scoring_criteria JSONB, -- Maps condition -> score explanation
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create supplement recommendations junction table
CREATE TABLE supplement_recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    health_tag_id UUID REFERENCES health_tags(id) ON DELETE CASCADE,
    supplement_id UUID REFERENCES supplements(id) ON DELETE CASCADE,
    priority INTEGER DEFAULT 1, -- 1 = high, 2 = medium, 3 = low
    score INTEGER DEFAULT 1, -- From CSV scoring
    rationale TEXT, -- Why this supplement is recommended
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(health_tag_id, supplement_id)
);

-- Update food recommendations table
CREATE TABLE food_recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    health_tag_id UUID REFERENCES health_tags(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    benefits TEXT[],
    nutritional_info JSONB,
    serving_suggestions TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_supplements_name ON supplements(name);
CREATE INDEX idx_supplement_recommendations_health_tag ON supplement_recommendations(health_tag_id);
CREATE INDEX idx_supplement_recommendations_supplement ON supplement_recommendations(supplement_id);
CREATE INDEX idx_food_recommendations_health_tag ON food_recommendations(health_tag_id);

-- RLS Policies
ALTER TABLE supplements ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplement_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE food_recommendations ENABLE ROW LEVEL SECURITY;

-- Public read access for supplements
CREATE POLICY "Supplements are publicly readable" ON supplements FOR SELECT USING (true);
CREATE POLICY "Supplement recommendations are publicly readable" ON supplement_recommendations FOR SELECT USING (true);
CREATE POLICY "Food recommendations are publicly readable" ON food_recommendations FOR SELECT USING (true);

-- Admin write access
CREATE POLICY "Admins can manage supplements" ON supplements 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage supplement recommendations" ON supplement_recommendations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage food recommendations" ON food_recommendations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Update the process_quiz_results function to work with new schema
CREATE OR REPLACE FUNCTION process_quiz_results(user_answers JSONB)
RETURNS TABLE (
    health_tag_name TEXT,
    health_tag_description TEXT,
    recommendation_type TEXT,
    recommendation_id UUID,
    recommendation_name TEXT,
    recommendation_details JSONB,
    priority INTEGER,
    score INTEGER
) AS $$
BEGIN
    -- Return supplement recommendations
    RETURN QUERY
    SELECT 
        ht.tag_name as health_tag_name,
        ht.description as health_tag_description,
        'supplement'::TEXT as recommendation_type,
        sr.id as recommendation_id,
        s.name as recommendation_name,
        jsonb_build_object(
            'description', s.description,
            'benefits', s.benefits,
            'side_effects', s.side_effects,
            'contraindications', s.contraindications,
            'dosage_info', s.dosage_info,
            'interactions', s.interactions,
            'pregnancy_safe', s.pregnancy_safe,
            'breastfeeding_safe', s.breastfeeding_safe,
            'rationale', sr.rationale
        ) as recommendation_details,
        sr.priority,
        sr.score
    FROM health_tags ht
    JOIN supplement_recommendations sr ON sr.health_tag_id = ht.id
    JOIN supplements s ON s.id = sr.supplement_id
    WHERE EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(ht.triggers) as trigger_obj
        WHERE user_answers ? (trigger_obj->>'question_id')
        AND user_answers->(trigger_obj->>'question_id') ? (trigger_obj->>'answer')
    )
    
    UNION ALL
    
    -- Return food recommendations
    SELECT 
        ht.tag_name as health_tag_name,
        ht.description as health_tag_description,
        'food'::TEXT as recommendation_type,
        fr.id as recommendation_id,
        fr.name as recommendation_name,
        jsonb_build_object(
            'description', fr.description,
            'benefits', fr.benefits,
            'nutritional_info', fr.nutritional_info,
            'serving_suggestions', fr.serving_suggestions
        ) as recommendation_details,
        2 as priority, -- Food recommendations are medium priority
        1 as score
    FROM health_tags ht
    JOIN food_recommendations fr ON fr.health_tag_id = ht.id
    WHERE EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(ht.triggers) as trigger_obj
        WHERE user_answers ? (trigger_obj->>'question_id')
        AND user_answers->(trigger_obj->>'question_id') ? (trigger_obj->>'answer')
    )
    
    ORDER BY priority ASC, score DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;