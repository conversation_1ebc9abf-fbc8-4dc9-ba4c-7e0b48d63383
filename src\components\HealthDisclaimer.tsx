import { AlertTriangle, Shield } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface HealthDisclaimerProps {
  variant?: 'default' | 'compact' | 'prominent'
  className?: string
}

export function HealthDisclaimer({ variant = 'default', className = '' }: HealthDisclaimerProps) {
  if (variant === 'compact') {
    return (
      <div className={`bg-orange-50 border border-orange-200 rounded-lg p-3 ${className}`}>
        <p className="text-xs text-orange-800 leading-relaxed">
          <strong>Medical Disclaimer:</strong> Our recommendations are for educational purposes only and do not constitute medical advice. 
          Always consult with a healthcare professional before starting any supplement regimen.
        </p>
      </div>
    )
  }

  if (variant === 'prominent') {
    return (
      <Alert variant="destructive" className={`border-orange-200 bg-orange-50 ${className}`}>
        <AlertTriangle className="h-4 w-4 text-orange-600" />
        <AlertDescription className="text-orange-800">
          <div className="space-y-2">
            <p className="font-semibold">Important Medical Disclaimer</p>
            <div className="text-sm space-y-1">
              <p>• Our recommendations are for informational and educational purposes only</p>
              <p>• This is not medical advice, diagnosis, or treatment</p>
              <p>• Always consult with qualified healthcare professionals before making health decisions</p>
              <p>• Individual results may vary based on personal health conditions</p>
              <p>• Supplement statements have not been evaluated by the FDA</p>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    )
  }

  // Default variant
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start gap-3">
        <Shield className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
        <div>
          <h4 className="font-semibold text-blue-800 mb-2">Health & Safety Information</h4>
          <div className="text-sm text-blue-700 space-y-2">
            <p>
              <strong>Not Medical Advice:</strong> Our health assessments and recommendations are for educational purposes only 
              and do not constitute medical advice, diagnosis, or treatment.
            </p>
            <p>
              <strong>Consult Healthcare Providers:</strong> Always consult with qualified healthcare professionals before 
              starting any supplement regimen, especially if you have medical conditions or take medications.
            </p>
            <p>
              <strong>Individual Results May Vary:</strong> Recommendations are based on general health principles and may not 
              be suitable for everyone. Your healthcare provider can provide personalized medical guidance.
            </p>
            <p className="text-xs text-blue-600 mt-3">
              <strong>FDA Disclaimer:</strong> Statements regarding supplements have not been evaluated by the Food and Drug Administration 
              and are not intended to diagnose, treat, cure, or prevent any disease.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}