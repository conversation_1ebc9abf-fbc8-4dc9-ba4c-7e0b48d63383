import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/AuthContext'
import { CheckCircle, ArrowRight, Mail, Lock, User } from 'lucide-react'

interface AccountCreationProps {
  email: string
  answers: Record<string, any>
  onComplete: () => void
  onSkip: () => void
}

export function AccountCreation({ email, onComplete, onSkip }: AccountCreationProps) {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const { signUp } = useAuth()

  const handleCreateAccount = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validation
    if (password.length < 6) {
      setError('Password must be at least 6 characters long')
      setLoading(false)
      return
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    try {
      const { error } = await signUp(email, password, fullName)
      
      if (error) {
        if (error.message.includes('already registered')) {
          setError('An account with this email already exists. You can sign in instead.')
        } else {
          setError(error.message)
        }
      } else {
        setSuccess(true)
        // Wait a moment to show success message, then complete
        setTimeout(() => {
          onComplete()
        }, 2000)
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-2xl bg-white/95 backdrop-blur-sm border border-emerald-200/50">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Account Created!</h2>
              <p className="text-gray-600">
                Welcome to LifeSupplier! Your account has been created successfully.
              </p>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <p className="text-sm text-green-800">
                <strong>Check your email</strong> for a confirmation link to verify your account.
                Your health report will be available in your dashboard.
              </p>
            </div>
            <p className="text-sm text-gray-500">
              Redirecting to your results...
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex items-center justify-center p-4">
      {/* Logo */}
      <div className="absolute top-6 left-6">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
            <div className="w-4 h-4 bg-white rounded-sm"></div>
          </div>
          <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
        </div>
      </div>

      <div className="w-full max-w-md">
        <Card className="shadow-2xl bg-white/95 backdrop-blur-sm border border-emerald-200/50">
          <CardHeader className="text-center pb-6">
            <div className="mb-4">
              <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <User className="w-6 h-6 text-emerald-600" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Create Your Account
            </CardTitle>
            <CardDescription className="text-base">
              Save your health report and unlock free future quizzes
            </CardDescription>
          </CardHeader>
          
          <CardContent className="px-8 pb-8">
            {/* Benefits */}
            <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-emerald-800 mb-2">Account Benefits:</h3>
              <ul className="text-sm text-emerald-700 space-y-1">
                <li>• Save your personalized health report</li>
                <li>• Take unlimited free quizzes</li>
                <li>• Access your health history anytime</li>
                <li>• Get updated recommendations</li>
              </ul>
            </div>

            <form onSubmit={handleCreateAccount} className="space-y-4">
              {/* Pre-filled Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-emerald-600" />
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  disabled
                  className="bg-gray-50 text-gray-600"
                />
                <p className="text-xs text-gray-500">
                  Using email from your quiz submission
                </p>
              </div>

              {/* Full Name */}
              <div className="space-y-2">
                <Label htmlFor="fullName" className="flex items-center gap-2">
                  <User className="w-4 h-4 text-emerald-600" />
                  Full Name (Optional)
                </Label>
                <Input
                  id="fullName"
                  type="text"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="border-emerald-200 focus:border-emerald-500"
                />
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password" className="flex items-center gap-2">
                  <Lock className="w-4 h-4 text-emerald-600" />
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Create a secure password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={6}
                  className="border-emerald-200 focus:border-emerald-500"
                />
                <p className="text-xs text-gray-500">
                  Must be at least 6 characters long
                </p>
              </div>

              {/* Confirm Password */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="flex items-center gap-2">
                  <Lock className="w-4 h-4 text-emerald-600" />
                  Confirm Password
                </Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="border-emerald-200 focus:border-emerald-500"
                />
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md border border-red-200">
                  {error}
                </div>
              )}

              <div className="space-y-3 pt-4">
                <Button 
                  type="submit" 
                  className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-3"
                  disabled={loading}
                >
                  {loading ? (
                    'Creating Account...'
                  ) : (
                    <>
                      Create Account & Save Report
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>

                <Button
                  type="button"
                  variant="ghost"
                  onClick={onSkip}
                  className="w-full text-gray-600 hover:text-gray-800"
                  disabled={loading}
                >
                  Skip for now - Continue to results
                </Button>
              </div>
            </form>

            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <h4 className="font-semibold text-blue-800 text-sm mb-2">Account & Privacy Information</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• Your data is encrypted and stored securely</li>
                  <li>• We never sell your information to third parties</li>
                  <li>• You can delete your account and data anytime</li>
                  <li>• Access your data or update preferences from your dashboard</li>
                </ul>
              </div>
              
              <div className="text-xs text-gray-600 text-center space-y-2">
                <p>
                  By creating an account, you confirm you have read and agree to our{' '}
                  <span 
                    className="text-emerald-600 hover:text-emerald-700 underline cursor-pointer"
                    onClick={() => window.open('/terms', '_blank')}
                  >
                    Terms of Service
                  </span>
                  {' '}and{' '}
                  <span 
                    className="text-emerald-600 hover:text-emerald-700 underline cursor-pointer"
                    onClick={() => window.open('/privacy', '_blank')}
                  >
                    Privacy Policy
                  </span>.
                </p>
                <p className="text-gray-500">
                  Questions? Contact <NAME_EMAIL>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}