import { describe, it, expect } from 'vitest'
import { processQuizAnswers } from '../../server/quiz'
import { QUIZ_SCENARIOS, QuizScenario } from '../test-data/predefinedQuizScenarios'

describe('Quiz Processing Logic', () => {
  describe('processQuizAnswers', () => {
    it('should return general wellness for healthy person', () => {
      const healthyScenario = QUIZ_SCENARIOS.find(s => s.name === 'Healthy Person')!
      const results = processQuizAnswers(healthyScenario.answers)
      
      expect(results).toHaveLength(1)
      expect(results[0].health_tag_name).toBe('General Wellness')
      expect(results[0].recommendation_type).toBe('supplement')
      expect(results[0].recommendation_name).toBe('High-Quality Multivitamin')
    })

    it('should identify low energy condition correctly', () => {
      const lowEnergyScenario = QUIZ_SCENARIOS.find(s => s.name === 'Low Energy Person')!
      const results = processQuizAnswers(lowEnergyScenario.answers)
      
      const lowEnergyResults = results.filter(r => r.health_tag_name === 'Low Energy')
      expect(lowEnergyResults.length).toBeGreaterThan(0)
    })

    it('should identify joint issues correctly', () => {
      const jointScenario = QUIZ_SCENARIOS.find(s => s.name === 'Joint Issues Person')!
      const results = processQuizAnswers(jointScenario.answers)
      
      const jointResults = results.filter(r => r.health_tag_name === 'Joint Support Needed')
      expect(jointResults.length).toBeGreaterThan(0)
    })

    it('should identify stress management needs', () => {
      const stressScenario = QUIZ_SCENARIOS.find(s => s.name === 'High Stress Person')!
      const results = processQuizAnswers(stressScenario.answers)
      
      const stressResults = results.filter(r => r.health_tag_name === 'Stress Management')
      expect(stressResults.length).toBeGreaterThan(0)
    })

    it('should identify multiple conditions for complex scenario', () => {
      const multipleIssuesScenario = QUIZ_SCENARIOS.find(s => s.name === 'Multiple Issues Person')!
      const results = processQuizAnswers(multipleIssuesScenario.answers)
      
      // Should identify multiple health conditions
      expect(results.length).toBeGreaterThan(5)
      
      // Check for expected conditions
      const conditionNames = results.map(r => r.health_tag_name)
      expect(conditionNames).toContain('Low Energy')
      expect(conditionNames).toContain('Sleep Quality Issues')
      expect(conditionNames).toContain('Joint Support Needed')
      expect(conditionNames).toContain('Stress Management')
    })

    it('should sort results by priority score', () => {
      const multipleIssuesScenario = QUIZ_SCENARIOS.find(s => s.name === 'Multiple Issues Person')!
      const results = processQuizAnswers(multipleIssuesScenario.answers)
      
      // Results should be sorted by priority score descending
      for (let i = 0; i < results.length - 1; i++) {
        expect(results[i].priority_score! >= results[i + 1].priority_score!).toBe(true)
      }
    })

    it('should include both supplements and foods in recommendations', () => {
      const multipleIssuesScenario = QUIZ_SCENARIOS.find(s => s.name === 'Multiple Issues Person')!
      const results = processQuizAnswers(multipleIssuesScenario.answers)
      
      const supplementResults = results.filter(r => r.recommendation_type === 'supplement')
      const foodResults = results.filter(r => r.recommendation_type === 'food')
      
      expect(supplementResults.length).toBeGreaterThan(0)
      expect(foodResults.length).toBeGreaterThan(0)
    })

    it('should handle empty answers gracefully', () => {
      const results = processQuizAnswers({})
      
      // Should return general wellness when no conditions are identified
      expect(results).toHaveLength(1)
      expect(results[0].health_tag_name).toBe('General Wellness')
    })
  })

  describe('All Quiz Scenarios', () => {
    QUIZ_SCENARIOS.forEach((scenario: QuizScenario) => {
      it(`should process "${scenario.name}" scenario without errors`, () => {
        expect(() => {
          const results = processQuizAnswers(scenario.answers)
          expect(results).toBeDefined()
          expect(Array.isArray(results)).toBe(true)
          expect(results.length).toBeGreaterThan(0)
        }).not.toThrow()
      })

      it(`should include expected conditions for "${scenario.name}"`, () => {
        const results = processQuizAnswers(scenario.answers)
        const resultConditions = results.map(r => r.health_tag_name)
        
        scenario.expectedConditions.forEach(expectedCondition => {
          expect(resultConditions).toContain(expectedCondition)
        })
      })

      it(`should have valid recommendation structure for "${scenario.name}"`, () => {
        const results = processQuizAnswers(scenario.answers)
        
        results.forEach(result => {
          expect(result.health_tag_name).toBeDefined()
          expect(result.health_tag_description).toBeDefined()
          expect(result.recommendation_type).toMatch(/^(supplement|food)$/)
          expect(result.recommendation_id).toBeDefined()
          expect(result.recommendation_name).toBeDefined()
          expect(result.recommendation_details).toBeDefined()
          expect(result.recommendation_details.description).toBeDefined()
          expect(Array.isArray(result.recommendation_details.benefits)).toBe(true)
          expect(typeof result.priority_score).toBe('number')
        })
      })
    })
  })

  describe('Priority Scoring System', () => {
    it('should assign higher priority scores to earlier conditions', () => {
      const multipleIssuesScenario = QUIZ_SCENARIOS.find(s => s.name === 'Multiple Issues Person')!
      const results = processQuizAnswers(multipleIssuesScenario.answers)
      
      // First supplement should have higher priority than later ones
      const supplementResults = results.filter(r => r.recommendation_type === 'supplement')
      if (supplementResults.length >= 2) {
        expect(supplementResults[0].priority_score! > supplementResults[1].priority_score!).toBe(true)
      }
    })

    it('should prioritize supplements over foods', () => {
      const multipleIssuesScenario = QUIZ_SCENARIOS.find(s => s.name === 'Multiple Issues Person')!
      const results = processQuizAnswers(multipleIssuesScenario.answers)
      
      const supplementResults = results.filter(r => r.recommendation_type === 'supplement')
      const foodResults = results.filter(r => r.recommendation_type === 'food')
      
      if (supplementResults.length > 0 && foodResults.length > 0) {
        // Generally supplements should have higher priority scores than foods
        const avgSupplementPriority = supplementResults.reduce((sum, r) => sum + (r.priority_score || 0), 0) / supplementResults.length
        const avgFoodPriority = foodResults.reduce((sum, r) => sum + (r.priority_score || 0), 0) / foodResults.length
        
        expect(avgSupplementPriority).toBeGreaterThan(avgFoodPriority)
      }
    })
  })

  describe('Recommendation Details', () => {
    it('should include dosage information for supplements', () => {
      const lowEnergyScenario = QUIZ_SCENARIOS.find(s => s.name === 'Low Energy Person')!
      const results = processQuizAnswers(lowEnergyScenario.answers)
      
      const supplementResults = results.filter(r => r.recommendation_type === 'supplement')
      
      supplementResults.forEach(result => {
        expect(result.recommendation_details.dosage_info).toBeDefined()
        if (result.recommendation_details.dosage_info) {
          expect(result.recommendation_details.dosage_info.timing).toBeDefined()
          expect(typeof result.recommendation_details.dosage_info.with_food).toBe('boolean')
        }
      })
    })

    it('should include serving suggestions for foods', () => {
      const multipleIssuesScenario = QUIZ_SCENARIOS.find(s => s.name === 'Multiple Issues Person')!
      const results = processQuizAnswers(multipleIssuesScenario.answers)
      
      const foodResults = results.filter(r => r.recommendation_type === 'food')
      
      foodResults.forEach(result => {
        expect(Array.isArray(result.recommendation_details.serving_suggestions)).toBe(true)
      })
    })

    it('should include safety information for supplements', () => {
      const lowEnergyScenario = QUIZ_SCENARIOS.find(s => s.name === 'Low Energy Person')!
      const results = processQuizAnswers(lowEnergyScenario.answers)
      
      const supplementResults = results.filter(r => r.recommendation_type === 'supplement')
      
      supplementResults.forEach(result => {
        expect(typeof result.recommendation_details.pregnancy_safe).toBe('boolean')
        expect(typeof result.recommendation_details.breastfeeding_safe).toBe('boolean')
        expect(Array.isArray(result.recommendation_details.side_effects)).toBe(true)
        expect(Array.isArray(result.recommendation_details.contraindications)).toBe(true)
        expect(Array.isArray(result.recommendation_details.interactions)).toBe(true)
      })
    })
  })
})