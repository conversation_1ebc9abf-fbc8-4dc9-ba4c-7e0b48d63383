// Test the Enhanced Scoring System: +120 bonus for each additional condition
// Comparing OLD vs NEW scoring for Low Blood Sugar + Cardiovascular conditions

const userConditions = ["Low Energy & Blood Sugar", "Cardiovascular Health Support"];

// Supplements available for these conditions
const SUPPLEMENTS = [
  {
    name: "Ceylon Cinnamon",
    condition_names: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"],
    type: "MULTI-CONDITION"
  },
  {
    name: "B-Complex Vitamins", 
    condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus"],
    type: "SINGLE-CONDITION"
  },
  {
    name: "Chromium Picolinate",
    condition_names: ["Low Energy & Blood Sugar", "Weight Management Support"],
    type: "SINGLE-CONDITION"
  },
  {
    name: "Hawthorn Berry",
    condition_names: ["Cardiovascular Health Support", "Digestive Health Support"],
    type: "SINGLE-CONDITION"
  },
  {
    name: "Green Tea Extract",
    condition_names: ["Weight Management Support", "Cardiovascular Health Support"],
    type: "SINGLE-CONDITION"
  }
];

// OLD Scoring System (current)
function calculateOldScore(supplement, userConditions) {
  const coverageConditions = supplement.condition_names.filter(cond => userConditions.includes(cond));
  return coverageConditions.length > 0 ? 100 + (coverageConditions.length * 20) : 0;
}

// NEW Enhanced Scoring System (+120 for each additional condition)
function calculateNewScore(supplement, userConditions) {
  const coverageConditions = supplement.condition_names.filter(cond => userConditions.includes(cond));
  
  if (coverageConditions.length === 0) return 0;
  
  let score = 100; // Base score for covering at least 1 condition
  
  // Add +120 bonus for each additional condition beyond the first
  if (coverageConditions.length > 1) {
    score += (coverageConditions.length - 1) * 120;
  }
  
  return score;
}

// Weighted Random Selection Function
function weightedRandomSelection(supplements, scores) {
  const totalWeight = scores.reduce((sum, score) => sum + score, 0);
  let random = Math.random() * totalWeight;
  
  for (let i = 0; i < supplements.length; i++) {
    random -= scores[i];
    if (random <= 0) return supplements[i];
  }
  return supplements[supplements.length - 1];
}

console.log("🚀 ENHANCED SCORING SYSTEM TEST");
console.log("User Conditions: Low Blood Sugar + Cardiovascular Health");
console.log("=".repeat(80));

// Calculate scores for both systems
const oldScores = SUPPLEMENTS.map(s => calculateOldScore(s, userConditions));
const newScores = SUPPLEMENTS.map(s => calculateNewScore(s, userConditions));

console.log("\n📊 SCORING COMPARISON:");
console.log("-".repeat(80));
console.log("Supplement".padEnd(20) + " | Old Score | New Score | Coverage | Improvement");
console.log("-".repeat(80));

SUPPLEMENTS.forEach((supplement, i) => {
  const coverageConditions = supplement.condition_names.filter(cond => userConditions.includes(cond));
  const coverage = coverageConditions.length;
  const improvement = newScores[i] > oldScores[i] ? `+${newScores[i] - oldScores[i]}` : "same";
  const typeIcon = supplement.type === "MULTI-CONDITION" ? "⭐" : "🔸";
  
  console.log(
    `${typeIcon} ${supplement.name.padEnd(17)} | ${oldScores[i].toString().padEnd(9)} | ${newScores[i].toString().padEnd(9)} | ${coverage} conds  | ${improvement}`
  );
});

// Calculate probabilities
const oldTotalWeight = oldScores.reduce((sum, score) => sum + score, 0);
const newTotalWeight = newScores.reduce((sum, score) => sum + score, 0);

console.log("\n🎯 PROBABILITY COMPARISON:");
console.log("-".repeat(70));
console.log("Supplement".padEnd(20) + " | Old Prob | New Prob | Change");
console.log("-".repeat(70));

SUPPLEMENTS.forEach((supplement, i) => {
  const oldProb = (oldScores[i] / oldTotalWeight * 100).toFixed(1);
  const newProb = (newScores[i] / newTotalWeight * 100).toFixed(1);
  const change = (parseFloat(newProb) - parseFloat(oldProb)).toFixed(1);
  const changeIcon = parseFloat(change) > 0 ? "📈" : parseFloat(change) < 0 ? "📉" : "➡️";
  const typeIcon = supplement.type === "MULTI-CONDITION" ? "⭐" : "🔸";
  
  console.log(
    `${typeIcon} ${supplement.name.padEnd(17)} | ${oldProb.padEnd(8)}% | ${newProb.padEnd(8)}% | ${changeIcon} ${change}%`
  );
});

// Simulation comparison
console.log("\n🧪 SIMULATION RESULTS (10,000 selections each):");
console.log("-".repeat(70));

const oldSimulation = {};
const newSimulation = {};
const simulations = 10000;

// Old system simulation
for (let i = 0; i < simulations; i++) {
  const selected = weightedRandomSelection(SUPPLEMENTS, oldScores);
  oldSimulation[selected.name] = (oldSimulation[selected.name] || 0) + 1;
}

// New system simulation
for (let i = 0; i < simulations; i++) {
  const selected = weightedRandomSelection(SUPPLEMENTS, newScores);
  newSimulation[selected.name] = (newSimulation[selected.name] || 0) + 1;
}

console.log("Supplement".padEnd(20) + " | Old System | New System | Difference");
console.log("-".repeat(70));

SUPPLEMENTS.forEach(supplement => {
  const oldCount = oldSimulation[supplement.name] || 0;
  const newCount = newSimulation[supplement.name] || 0;
  const oldPercent = (oldCount / simulations * 100).toFixed(1);
  const newPercent = (newCount / simulations * 100).toFixed(1);
  const diff = (parseFloat(newPercent) - parseFloat(oldPercent)).toFixed(1);
  const diffIcon = parseFloat(diff) > 0 ? "📈" : parseFloat(diff) < 0 ? "📉" : "➡️";
  const typeIcon = supplement.type === "MULTI-CONDITION" ? "⭐" : "🔸";
  
  console.log(
    `${typeIcon} ${supplement.name.padEnd(17)} | ${oldPercent.padEnd(10)}% | ${newPercent.padEnd(10)}% | ${diffIcon} ${diff}%`
  );
});

console.log("\n" + "=".repeat(80));
console.log("🎯 KEY IMPROVEMENTS:");
console.log("=".repeat(80));

const ceyalonOldProb = (oldScores[0] / oldTotalWeight * 100).toFixed(1);
const ceyalonNewProb = (newScores[0] / newTotalWeight * 100).toFixed(1);
const ceyalonImprovement = ((parseFloat(ceyalonNewProb) / parseFloat(ceyalonOldProb)) - 1) * 100;

console.log(`🥇 CEYLON CINNAMON (Multi-condition):`);
console.log(`   • OLD: ${ceyalonOldProb}% selection chance`);
console.log(`   • NEW: ${ceyalonNewProb}% selection chance`);
console.log(`   • IMPROVEMENT: ${ceyalonImprovement.toFixed(1)}% increase in probability!`);

console.log(`\n📊 SCORING BREAKDOWN:`);
console.log(`   • OLD: 100 + (2 conditions × 20) = 120 points`);
console.log(`   • NEW: 100 + (1 additional condition × 120) = 220 points`);
console.log(`   • BONUS: +100 points for multi-condition coverage!`);

console.log(`\n🎯 BENEFITS:`);
console.log(`   ✅ Multi-condition supplements get MUCH higher priority`);
console.log(`   ✅ Users more likely to get efficient, comprehensive solutions`);
console.log(`   ✅ Ceylon Cinnamon now has ${ceyalonNewProb}% vs ${ceyalonOldProb}% chance`);
console.log(`   ✅ Better user experience with more targeted recommendations`);
