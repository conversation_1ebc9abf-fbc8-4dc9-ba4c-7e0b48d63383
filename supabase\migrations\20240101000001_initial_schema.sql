-- ============================================================================
-- Initial Schema Migration
-- ============================================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE question_type AS ENUM ('multiple-choice', 'yes-no', 'scale');
CREATE TYPE recommendation_type AS ENUM ('supplement', 'food');
CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'past_due');
CREATE TYPE user_role AS ENUM ('user', 'admin');

-- ============================================================================
-- Table Creation (in dependency order)
-- ============================================================================

-- 1. Profiles table - extends auth.users with additional info
CREATE TABLE profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    role user_role DEFAULT 'user',
    newsletter_opt_in BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Questions table - stores all quiz questions (no dependencies)
CREATE TABLE questions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    text TEXT NOT NULL,
    type question_type NOT NULL,
    options TEXT[] NOT NULL,
    "order" INTEGER NOT NULL,
    parent_id UUID REFERENCES questions(id),
    condition TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Health tags table - maps answers to health issues (no dependencies)
CREATE TABLE health_tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tag_name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    triggers JSONB NOT NULL, -- Array of {question_id, answer} objects
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Recommendations table - depends on health_tags
CREATE TABLE recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    health_tag_id UUID REFERENCES health_tags(id) ON DELETE CASCADE,
    type recommendation_type NOT NULL,
    name TEXT NOT NULL,
    details JSONB NOT NULL, -- {dosage, timing, benefits, description}
    affiliate_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Responses table - depends on auth.users and questions
CREATE TABLE responses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
    answer JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Reports table - depends on auth.users
CREATE TABLE reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    quiz_responses JSONB NOT NULL,
    health_tags TEXT[],
    recommendations JSONB NOT NULL,
    pdf_url TEXT,
    payment_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Subscriptions table - depends on auth.users
CREATE TABLE subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_id TEXT NOT NULL UNIQUE,
    status subscription_status NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Indexes for Performance
-- ============================================================================

CREATE INDEX idx_questions_order ON questions("order");
CREATE INDEX idx_questions_parent_id ON questions(parent_id);
CREATE INDEX idx_responses_user_id ON responses(user_id);
CREATE INDEX idx_responses_question_id ON responses(question_id);
CREATE INDEX idx_reports_user_id ON reports(user_id);
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe_id ON subscriptions(stripe_id);
CREATE INDEX idx_profiles_user_id ON profiles(user_id);
CREATE INDEX idx_recommendations_health_tag_id ON recommendations(health_tag_id);

-- ============================================================================
-- Row Level Security (RLS) Policies
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON profiles 
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own profile" ON profiles 
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own profile" ON profiles 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Questions are public (readable by everyone)
CREATE POLICY "Questions are publicly readable" ON questions FOR SELECT USING (true);
-- Only admins can manage questions
CREATE POLICY "Admins can manage questions" ON questions 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Users can only read/write their own responses
CREATE POLICY "Users can manage their own responses" ON responses 
    FOR ALL USING (auth.uid() = user_id);

-- Health tags are public (readable by everyone)
CREATE POLICY "Health tags are publicly readable" ON health_tags FOR SELECT USING (true);
-- Only admins can manage health tags
CREATE POLICY "Admins can manage health tags" ON health_tags 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Recommendations are public (readable by everyone)
CREATE POLICY "Recommendations are publicly readable" ON recommendations FOR SELECT USING (true);
-- Only admins can manage recommendations
CREATE POLICY "Admins can manage recommendations" ON recommendations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Users can only read their own reports
CREATE POLICY "Users can read their own reports" ON reports 
    FOR SELECT USING (auth.uid() = user_id);
-- System can insert reports (for payment processing)
CREATE POLICY "System can insert reports" ON reports 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only read their own subscriptions
CREATE POLICY "Users can read their own subscriptions" ON subscriptions 
    FOR SELECT USING (auth.uid() = user_id);
-- System can manage subscriptions (for Stripe webhooks)
CREATE POLICY "System can manage subscriptions" ON subscriptions 
    FOR ALL WITH CHECK (true);

-- ============================================================================
-- Functions and Triggers
-- ============================================================================

-- Function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (user_id, full_name)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
EXCEPTION
    WHEN others THEN
        -- Log error but don't fail user creation
        RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to create profile on user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Trigger to update updated_at on profiles
CREATE OR REPLACE TRIGGER on_profiles_updated
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- Helper function to get questions in order
CREATE OR REPLACE FUNCTION get_quiz_questions()
RETURNS TABLE (
    id UUID,
    text TEXT,
    type question_type,
    options TEXT[],
    question_order INTEGER,
    parent_id UUID,
    condition TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        q.id,
        q.text,
        q.type,
        q.options,
        q."order" as question_order,
        q.parent_id,
        q.condition
    FROM questions q
    ORDER BY q."order" ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process quiz answers and get recommendations
CREATE OR REPLACE FUNCTION process_quiz_results(user_answers JSONB)
RETURNS TABLE (
    health_tag_name TEXT,
    health_tag_description TEXT,
    recommendation_id UUID,
    recommendation_type recommendation_type,
    recommendation_name TEXT,
    recommendation_details JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ht.tag_name as health_tag_name,
        ht.description as health_tag_description,
        r.id as recommendation_id,
        r.type as recommendation_type,
        r.name as recommendation_name,
        r.details as recommendation_details
    FROM health_tags ht
    JOIN recommendations r ON r.health_tag_id = ht.id
    WHERE EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(ht.triggers) as trigger_obj
        WHERE user_answers ? (trigger_obj->>'question_id')
        AND user_answers->(trigger_obj->>'question_id') ? (trigger_obj->>'answer')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;