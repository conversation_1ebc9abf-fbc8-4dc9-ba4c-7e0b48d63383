export interface QuizScenario {
  name: string
  description: string
  answers: Record<string, string>
  expectedConditions: string[]
  expectedSupplementTypes?: string[]
  expectedFoodTypes?: string[]
}

// Helper function to create basic demographic answers
const basicDemographics = {
  '550e8400-e29b-41d4-a716-446655440001': 'Female', // Gender
  '550e8400-e29b-41d4-a716-446655440002': '18-45',  // Age
}

export const QUIZ_SCENARIOS: QuizScenario[] = [
  {
    name: 'Low Energy Person',
    description: 'Someone experiencing fatigue, poor concentration, and energy crashes',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'No', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'No', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'No', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'Yes', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'Neutral/Not sure', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'Yes', // Joints free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes', // Joints flexible
      '550e8400-e29b-41d4-a716-446655440006': 'Yes', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'Neutral/Not sure', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'Yes', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'Yes', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Yes', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Neutral/Not sure', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Yes', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Yes', // Metabolism works well
    },
    expectedConditions: ['Low Energy'],
    expectedSupplementTypes: ['B-Complex Vitamins'],
  },

  {
    name: 'Joint Issues Person',
    description: 'Someone with joint pain, stiffness, and mobility issues',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'Yes', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'Yes', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'Yes', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'Yes', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'No', // Joints NOT free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'No', // Joints NOT flexible
      '550e8400-e29b-41d4-a716-446655440006': 'Neutral/Not sure', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'Yes', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'Yes', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'Yes', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Yes', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Yes', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Yes', // Metabolism works well
    },
    expectedConditions: ['Joint Support Needed'],
  },

  {
    name: 'High Stress Person',
    description: 'Someone experiencing chronic stress and poor work-life balance',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'Yes', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'Neutral/Not sure', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'Neutral/Not sure', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'No', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'Yes', // Joints free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes', // Joints flexible
      '550e8400-e29b-41d4-a716-446655440006': 'Yes', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'Yes', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'No', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'No', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'Yes', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Yes', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Yes', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Yes', // Metabolism works well
    },
    expectedConditions: ['Stress Management', 'Sleep Quality Issues'],
  },

  {
    name: 'Healthy Person',
    description: 'Someone with minimal health concerns, should get general wellness recommendations',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'Yes', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'Yes', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'Yes', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'Yes', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'Yes', // Joints free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes', // Joints flexible
      '550e8400-e29b-41d4-a716-446655440006': 'Yes', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'Yes', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'Yes', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'Yes', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Yes', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Yes', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Yes', // Metabolism works well
    },
    expectedConditions: ['General Wellness'],
    expectedSupplementTypes: ['High-Quality Multivitamin'],
  },

  {
    name: 'Multiple Issues Person',
    description: 'Someone with overlapping health concerns: fatigue, sleep, joints, stress, digestion',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'No', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'No', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'No', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'No', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'No', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'No', // Joints free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'No', // Joints flexible
      '550e8400-e29b-41d4-a716-446655440006': 'No', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'No', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'No', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'No', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'No', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'No', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'No', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Neutral/Not sure', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'No', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'No', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'No', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Neutral/Not sure', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Neutral/Not sure', // Metabolism works well
    },
    expectedConditions: [
      'Low Energy',
      'Sleep Quality Issues',
      'Joint Support Needed',
      'Physical Activity Support',
      'Stress Management',
      'Digestive Health Support',
      'Immune System Support',
      'Hair & Nail Health',
      'Breathing Health'
    ],
  },

  {
    name: 'Active Person with Recovery Issues',
    description: 'Someone who exercises but has poor recovery and stamina',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'Neutral/Not sure', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'Yes', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'Yes', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'No', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'Neutral/Not sure', // Joints free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes', // Joints flexible
      '550e8400-e29b-41d4-a716-446655440006': 'Neutral/Not sure', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'No', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'Yes', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'Yes', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Yes', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Yes', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Yes', // Metabolism works well
    },
    expectedConditions: ['Physical Activity Support', 'Sleep Quality Issues'],
  },

  {
    name: 'Digestive Issues Person',
    description: 'Someone with digestive problems and related symptoms',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'Yes', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'Yes', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'Yes', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'Yes', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'Yes', // Joints free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes', // Joints flexible
      '550e8400-e29b-41d4-a716-446655440006': 'Yes', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'Yes', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'Yes', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'No', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Neutral/Not sure', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Yes', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Yes', // Metabolism works well
    },
    expectedConditions: ['Digestive Health Support'],
  },

  {
    name: 'Immune System Issues Person',
    description: 'Someone who gets sick frequently and has poor immunity',
    answers: {
      ...basicDemographics,
      '550e8400-e29b-41d4-a716-446655440003': 'Yes', // Avoid afternoon crashes
      '33ddc48a-3741-428b-b877-173b0168ebf9': 'Yes', // Can focus well
      '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes', // Stable without sugar cravings
      '550e8400-e29b-41d4-a716-446655440004': 'Yes', // Sleep well
      '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'Yes', // Wake up refreshed
      '550e8400-e29b-41d4-a716-446655440005': 'Yes', // Joints free from pain
      'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes', // Joints flexible
      '550e8400-e29b-41d4-a716-446655440006': 'Yes', // Can climb stairs
      'ce586653-1155-4563-839f-266623795bae': 'Yes', // Feel strong
      '550e8400-e29b-41d4-a716-446655440008': 'Yes', // Feel calm
      '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes', // Stay calm
      '550e8400-e29b-41d4-a716-446655440009': 'Yes', // Comfortable digestion
      '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'No', // Don't get sick easily
      'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'No', // Stable energy between meals
      'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // Drink enough water
      'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // Hair healthy
      'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // Breathe comfortably
      'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes', // Skip coffee
      '550e8400-e29b-41d4-a716-446655440011': 'Yes', // Free from heart issues
      '550e8400-e29b-41d4-a716-446655440012': 'Yes', // Free from circulation problems
      '550e8400-e29b-41d4-a716-446655440013': 'Yes', // Satisfied with weight
      '550e8400-e29b-41d4-a716-446655440014': 'Yes', // Metabolism works well
    },
    expectedConditions: ['Immune System Support'],
  },
]

// Helper function to get a scenario by name
export function getScenarioByName(name: string): QuizScenario | undefined {
  return QUIZ_SCENARIOS.find(scenario => scenario.name === name)
}

// Helper function to get all scenario names
export function getScenarioNames(): string[] {
  return QUIZ_SCENARIOS.map(scenario => scenario.name)
}