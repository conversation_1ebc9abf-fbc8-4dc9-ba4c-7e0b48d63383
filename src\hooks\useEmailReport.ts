import { useState } from 'react'
import { supabase } from '@/lib/supabase'

interface EmailReportData {
  healthTags: Array<{
    name: string
    description: string
  }>
  supplements: Array<{
    name: string
    description: string
    benefits: string[]
    dosage_info?: {
      min_dose?: string
      max_dose?: string
      timing?: string
      form?: string
      with_food?: boolean
    }
    side_effects?: string[]
    contraindications?: string[]
    interactions?: string[]
    pregnancy_safe?: boolean
    breastfeeding_safe?: boolean
    rationale?: string
    priority?: number
    score?: number
    condition_count?: number
    all_conditions?: string[]
  }>
  foods: Array<{
    name: string
    description: string
    benefits: string[]
    serving_suggestions?: string[]
    nutritional_info?: any
  }>
  currentDate: string
}

export function useEmailReport() {
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const sendReport = async (email: string, reportData: EmailReportData) => {
    setSending(true)
    setError(null)

    try {
      const { data, error: functionError } = await supabase.functions.invoke('send-health-report-email', {
        body: {
          to: email,
          reportData
        }
      })

      if (functionError) {
        throw new Error(functionError.message || 'Failed to send email')
      }

      console.log('Email sent successfully:', data)
      return { success: true, data }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send email'
      setError(errorMessage)
      console.error('Error sending email:', err)
      return { success: false, error: errorMessage }
    } finally {
      setSending(false)
    }
  }

  return {
    sendReport,
    sending,
    error
  }
}
