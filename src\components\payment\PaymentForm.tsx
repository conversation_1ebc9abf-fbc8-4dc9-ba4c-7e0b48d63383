import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Lock, CreditCard } from 'lucide-react'

interface PaymentFormProps {
  email: string
  onPaymentSuccess: () => void
  onPaymentError: (error: string) => void
  isProcessing: boolean
  setIsProcessing: (processing: boolean) => void
  quizAnswers?: Record<string, any>
}

export function PaymentForm({ 
  email,
  onPaymentError, 
  isProcessing, 
  setIsProcessing,
  quizAnswers = {}
}: PaymentFormProps) {
  const [paymentError, setPaymentError] = useState<string>('')

  const handleCheckout = async () => {
    console.log('PaymentForm: Starting checkout process')
    console.log('PaymentForm: Quiz answers count:', Object.keys(quizAnswers).length)
    console.log('PaymentForm: Customer email:', email)
    
    setIsProcessing(true)
    setPaymentError('')

    try {
      // Create checkout session using Supabase Edge Function
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-payment-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Origin': window.location.origin,
        },
        body: JSON.stringify({
          customer_email: email,
          quiz_answers: quizAnswers,
          metadata: {
            product: 'health_report',
            subscription_type: '7_day_trial',
            customer_email: email
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.details || `Server error: ${response.status}`)
      }

      const { checkout_url } = await response.json()

      console.log('PaymentForm: Checkout session created successfully')
      console.log('PaymentForm: Redirecting to Stripe checkout')
      
      // Redirect to Stripe Checkout
      window.location.href = checkout_url

    } catch (error) {
      console.error('Checkout error:', error)
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      setPaymentError(errorMessage)
      onPaymentError(errorMessage)
      setIsProcessing(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Payment Method Info Card */}
      <Card className="border-2 border-gray-200">
        <CardContent className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <CreditCard className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Secure Checkout</h3>
          </div>
          
          <div className="text-sm text-gray-600 space-y-2">
            <p>• 7-day free trial, then $19.99/month</p>
            <p>• Credit & Debit Cards, Apple Pay & Google Pay</p>
            <p>• Cancel anytime in your account</p>
            <p>• Processed securely by Stripe</p>
          </div>
          
          {paymentError && (
            <div className="mt-3 text-sm text-red-600 flex items-start gap-2">
              <span className="text-red-500">⚠️</span>
              <span>{paymentError}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Button */}
      <Button
        onClick={handleCheckout}
        disabled={isProcessing}
        className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
      >
        <Lock className="w-4 h-4 md:w-5 md:h-5 mr-2" />
{isProcessing ? 'Redirecting to Checkout...' : 'Start 7-Day Free Trial'}
      </Button>

      {/* Security Notice */}
      <div className="text-center text-sm text-gray-600">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Lock className="w-4 h-4" />
          <span>Secured by Stripe</span>
        </div>
        <p>You'll be redirected to Stripe's secure checkout page.</p>
      </div>

      {/* Test Mode Notice (Development) */}
      {import.meta.env.DEV && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800 font-medium mb-2">Test Mode:</p>
          <div className="text-xs text-blue-700 space-y-1">
            <div>• Use test card: 4242 4242 4242 4242</div>
            <div>• Any future expiry date and CVC</div>
            <div>• Apple Pay/Google Pay work in test mode</div>
          </div>
        </div>
      )}
    </div>
  )
}