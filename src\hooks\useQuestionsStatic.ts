import { useState } from 'react'

interface Question {
  id: string
  text: string
  type: 'yes-no' | 'multiple-choice'
  options: string[]
  order: number
}

// Comprehensive quiz questions based on the CSV data
const QUIZ_QUESTIONS: Question[] = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    text: 'What is your gender?',
    type: 'multiple-choice',
    options: ['Female', 'Male', 'Other/Prefer not to say'],
    order: 1
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    text: 'What is your age range?',
    type: 'multiple-choice',
    options: ['Under 18', '18-45', '46-65', '65+'],
    order: 2
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    text: 'Do you avoid afternoon crashes or feeling drained by mid-day?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 3
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440004',
    text: 'Do you sleep well without trouble?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 4
  },
  {
    id: '598022d6-cece-4d65-a457-dcfe80a3a1fb',
    text: 'I wake up feeling rested and refreshed.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 5
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440005',
    text: 'Are your joints free from pain and stiffness?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 6
  },
  {
    id: 'b941ea42-0943-49e1-95a3-462f3debcc03',
    text: 'My joints and hips feel flexible and comfortable.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 7
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440006',
    text: 'Can you climb stairs or do light activities without getting winded?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 8
  },
  {
    id: 'ce586653-1155-4563-839f-266623795bae',
    text: 'I feel physically strong and have good stamina.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 9
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440008',
    text: 'Do you feel calm and stress-free most of the time?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 10
  },
  {
    id: '5b3879a5-e825-4fff-b786-b7bc1b4cc025',
    text: 'Do you stay calm without feeling overwhelmed or anxious most days?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 11
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440009',
    text: 'Do you have comfortable digestion without bloating, stomach pain, or irregular bowel movements?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 12
  },
  {
    id: '33ddc48a-3741-428b-b877-173b0168ebf9',
    text: 'I can focus and concentrate well throughout the day.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 13
  },
  {
    id: '452ac791-288b-48aa-98ab-80d2173b2240',
    text: 'Do you feel stable without sugar cravings or sudden weakness?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 14
  },
  {
    id: '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc',
    text: 'I don\'t get easily sick or get the flu.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 15
  },
  {
    id: 'b2254912-7bb7-4428-a365-b7c0de7c8bf5',
    text: 'Do you maintain stable energy without frequent food cravings or weakness between meals?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 16
  },
  {
    id: 'f232fa4c-4268-4d41-8e67-e0b71d67c4bd',
    text: 'I drink enough water to stay hydrated every day.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 17
  },
  {
    id: 'aeda2a7e-b897-4bc0-a67f-20f1306c83d0',
    text: 'My hair is healthy and not thinning.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 18
  },
  {
    id: 'e2715890-51c7-4582-a89e-5007c3efb634',
    text: 'I breathe comfortably and deeply without difficulty.',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 19
  },
  {
    id: 'c19fca54-eef1-460c-9c2a-abb5753f39f6',
    text: 'Can you skip your usual coffee/tea without getting headaches or feeling irritable?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 20
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440011',
    text: 'Are you free from irregular heartbeat, chest tightness, or heart palpitations?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 21
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440012',
    text: 'Are you free from cold hands/feet, leg cramps, or circulation problems?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 22
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440013',  
    text: 'Are you satisfied with your current weight without trying to lose weight?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 23
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440014',
    text: 'Do you feel your metabolism works well without difficulty losing weight?',
    type: 'multiple-choice',
    options: ['Yes', 'Neutral/Not sure', 'No'],
    order: 24
  }
]

export function useQuestionsStatic() {
  const [questions] = useState<Question[]>(QUIZ_QUESTIONS)
  const [loading] = useState(false)
  const [error] = useState<string | null>(null)

  return { questions, loading, error }
}