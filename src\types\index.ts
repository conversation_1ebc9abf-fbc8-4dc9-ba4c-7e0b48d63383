export interface Question {
  id: string
  text: string
  type: 'multiple-choice' | 'yes-no' | 'scale'
  options: string[]
  order: number
  parent_id?: string
  condition?: string
}

export interface QuizResponse {
  id: string
  user_id: string
  question_id: string
  answer: any
  timestamp: string
}

export interface HealthTag {
  id: string
  tag_name: string
  description: string
  triggers: {
    question_id: string
    answer: string
  }[]
}

export interface Recommendation {
  id: string
  health_tag_id: string
  type: 'supplement' | 'food'
  name: string
  details: {
    dosage?: string
    timing?: string
    benefits: string[]
    description: string
  }
  affiliate_link?: string
}

export interface User {
  id: string
  email: string
  subscription_status?: 'active' | 'canceled' | 'trial'
  newsletter_opt_in: boolean
}

export interface Report {
  id: string
  user_id: string
  quiz_responses: Record<string, any>
  health_tags: string[]
  recommendations: Recommendation[]
  pdf_url?: string
  payment_id: string
  created_at: string
}

export interface Subscription {
  id: string
  user_id: string
  stripe_id: string
  status: 'active' | 'canceled' | 'past_due'
  price: number
  period_end: string
}