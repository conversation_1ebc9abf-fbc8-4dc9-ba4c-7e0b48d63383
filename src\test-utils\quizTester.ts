import { processQuizAnswers, QuizResult } from '../../server/quiz'
import { QuizScenario, QUIZ_SCENARIOS } from '../test-data/predefinedQuizScenarios'

export interface QuizTestResult {
  scenario: QuizScenario
  results: QuizResult[]
  executionTime: number
  conditionsFound: string[]
  supplementCount: number
  foodCount: number
  totalRecommendations: number
  highestPriority: number
  lowestPriority: number
  conditionsMatched: boolean
  conditionsMissed: string[]
  unexpectedConditions: string[]
}

export interface QuizTestSummary {
  totalScenarios: number
  successfulTests: number
  failedTests: number
  totalExecutionTime: number
  averageExecutionTime: number
  results: QuizTestResult[]
}

export class QuizTester {
  /**
   * Test a single quiz scenario and return detailed results
   */
  static testScenario(scenario: QuizScenario): QuizTestResult {
    const startTime = performance.now()
    
    try {
      const results = processQuizAnswers(scenario.answers)
      const endTime = performance.now()
      
      const conditionsFound = [...new Set(results.map(r => r.health_tag_name))]
      const supplementCount = results.filter(r => r.recommendation_type === 'supplement').length
      const foodCount = results.filter(r => r.recommendation_type === 'food').length
      const priorities = results.map(r => r.priority_score || 0)
      
      // Check if expected conditions were found
      const conditionsMissed = scenario.expectedConditions.filter(
        expected => !conditionsFound.includes(expected)
      )
      
      // Check for unexpected conditions (not in expected list)
      const unexpectedConditions = conditionsFound.filter(
        found => !scenario.expectedConditions.includes(found)
      )
      
      const conditionsMatched = conditionsMissed.length === 0
      
      return {
        scenario,
        results,
        executionTime: endTime - startTime,
        conditionsFound,
        supplementCount,
        foodCount,
        totalRecommendations: results.length,
        highestPriority: Math.max(...priorities, 0),
        lowestPriority: Math.min(...priorities, 0),
        conditionsMatched,
        conditionsMissed,
        unexpectedConditions
      }
    } catch (error) {
      throw new Error(`Failed to test scenario "${scenario.name}": ${error}`)
    }
  }

  /**
   * Test all quiz scenarios and return a summary
   */
  static testAllScenarios(): QuizTestSummary {
    const results: QuizTestResult[] = []
    let successfulTests = 0
    let failedTests = 0
    let totalExecutionTime = 0
    
    for (const scenario of QUIZ_SCENARIOS) {
      try {
        const result = this.testScenario(scenario)
        results.push(result)
        totalExecutionTime += result.executionTime
        
        if (result.conditionsMatched && result.totalRecommendations > 0) {
          successfulTests++
        } else {
          failedTests++
        }
      } catch (error) {
        console.error(`Failed to test scenario "${scenario.name}":`, error)
        failedTests++
      }
    }
    
    return {
      totalScenarios: QUIZ_SCENARIOS.length,
      successfulTests,
      failedTests,
      totalExecutionTime,
      averageExecutionTime: totalExecutionTime / QUIZ_SCENARIOS.length,
      results
    }
  }

  /**
   * Test specific scenarios by name
   */
  static testScenariosByName(names: string[]): QuizTestResult[] {
    const results: QuizTestResult[] = []
    
    for (const name of names) {
      const scenario = QUIZ_SCENARIOS.find(s => s.name === name)
      if (scenario) {
        try {
          const result = this.testScenario(scenario)
          results.push(result)
        } catch (error) {
          console.error(`Failed to test scenario "${name}":`, error)
        }
      } else {
        console.warn(`Scenario "${name}" not found`)
      }
    }
    
    return results
  }

  /**
   * Compare expected vs actual recommendations for a scenario
   */
  static compareRecommendations(scenario: QuizScenario, results: QuizResult[]): {
    matches: string[]
    missing: string[]
    extra: string[]
  } {
    const actualSupplements = results
      .filter(r => r.recommendation_type === 'supplement')
      .map(r => r.recommendation_name)
    
    const expectedSupplements = scenario.expectedSupplementTypes || []
    
    const matches = expectedSupplements.filter(expected => 
      actualSupplements.some(actual => 
        actual.toLowerCase().includes(expected.toLowerCase()) ||
        expected.toLowerCase().includes(actual.toLowerCase())
      )
    )
    
    const missing = expectedSupplements.filter(expected => 
      !actualSupplements.some(actual => 
        actual.toLowerCase().includes(expected.toLowerCase()) ||
        expected.toLowerCase().includes(actual.toLowerCase())
      )
    )
    
    const extra = actualSupplements.filter(actual =>
      !expectedSupplements.some(expected => 
        actual.toLowerCase().includes(expected.toLowerCase()) ||
        expected.toLowerCase().includes(actual.toLowerCase())
      )
    )
    
    return { matches, missing, extra }
  }

  /**
   * Generate a detailed report for a test result
   */
  static generateDetailedReport(testResult: QuizTestResult): string {
    const { scenario, results, executionTime, conditionsFound, supplementCount, foodCount } = testResult
    
    let report = `\n${'='.repeat(60)}\n`
    report += `QUIZ SCENARIO: ${scenario.name}\n`
    report += `${'='.repeat(60)}\n`
    report += `Description: ${scenario.description}\n`
    report += `Execution Time: ${executionTime.toFixed(2)}ms\n`
    report += `Total Recommendations: ${results.length} (${supplementCount} supplements, ${foodCount} foods)\n`
    
    if (testResult.conditionsMatched) {
      report += `✅ Expected Conditions: MATCHED\n`
    } else {
      report += `❌ Expected Conditions: NOT MATCHED\n`
      if (testResult.conditionsMissed.length > 0) {
        report += `   Missing: ${testResult.conditionsMissed.join(', ')}\n`
      }
    }
    
    if (testResult.unexpectedConditions.length > 0) {
      report += `⚠️  Unexpected Conditions: ${testResult.unexpectedConditions.join(', ')}\n`
    }
    
    report += `\nConditions Identified:\n`
    conditionsFound.forEach((condition, index) => {
      report += `  ${index + 1}. ${condition}\n`
    })
    
    report += `\nTop 5 Recommendations:\n`
    results.slice(0, 5).forEach((result, index) => {
      report += `  ${index + 1}. ${result.recommendation_name} (${result.recommendation_type})\n`
      report += `     Condition: ${result.health_tag_name}\n`
      report += `     Priority: ${result.priority_score}\n`
      if (result.recommendation_details.description) {
        const shortDesc = result.recommendation_details.description.length > 100 
          ? result.recommendation_details.description.substring(0, 100) + '...'
          : result.recommendation_details.description
        report += `     Description: ${shortDesc}\n`
      }
      report += `\n`
    })
    
    if (scenario.expectedSupplementTypes) {
      const comparison = this.compareRecommendations(scenario, results)
      report += `Expected vs Actual Supplements:\n`
      report += `  ✅ Matches: ${comparison.matches.join(', ') || 'None'}\n`
      report += `  ❌ Missing: ${comparison.missing.join(', ') || 'None'}\n`
      report += `  ➕ Extra: ${comparison.extra.join(', ') || 'None'}\n`
    }
    
    return report
  }

  /**
   * Generate a summary report for all test results
   */
  static generateSummaryReport(summary: QuizTestSummary): string {
    let report = `\n${'='.repeat(80)}\n`
    report += `QUIZ TESTING SUMMARY REPORT\n`
    report += `${'='.repeat(80)}\n`
    report += `Total Scenarios Tested: ${summary.totalScenarios}\n`
    report += `Successful Tests: ${summary.successfulTests}\n`
    report += `Failed Tests: ${summary.failedTests}\n`
    report += `Success Rate: ${((summary.successfulTests / summary.totalScenarios) * 100).toFixed(1)}%\n`
    report += `Total Execution Time: ${summary.totalExecutionTime.toFixed(2)}ms\n`
    report += `Average Execution Time: ${summary.averageExecutionTime.toFixed(2)}ms\n`
    
    report += `\nScenario Results:\n`
    summary.results.forEach((result) => {
      const status = result.conditionsMatched && result.totalRecommendations > 0 ? '✅' : '❌'
      report += `  ${status} ${result.scenario.name} (${result.totalRecommendations} recommendations, ${result.executionTime.toFixed(1)}ms)\n`
      
      if (!result.conditionsMatched) {
        if (result.conditionsMissed.length > 0) {
          report += `      Missing: ${result.conditionsMissed.join(', ')}\n`
        }
      }
    })
    
    report += `\nRecommendation Statistics:\n`
    const totalSupplements = summary.results.reduce((sum, r) => sum + r.supplementCount, 0)
    const totalFoods = summary.results.reduce((sum, r) => sum + r.foodCount, 0)
    const totalRecommendations = summary.results.reduce((sum, r) => sum + r.totalRecommendations, 0)
    
    report += `  Total Supplements Recommended: ${totalSupplements}\n`
    report += `  Total Foods Recommended: ${totalFoods}\n`
    report += `  Total Recommendations: ${totalRecommendations}\n`
    report += `  Average Recommendations per Scenario: ${(totalRecommendations / summary.totalScenarios).toFixed(1)}\n`
    
    return report
  }
}

/**
 * Helper function to quickly test and log results for development
 */
export function quickTestScenario(scenarioName: string): void {
  try {
    const scenario = QUIZ_SCENARIOS.find(s => s.name === scenarioName)
    if (!scenario) {
      console.error(`Scenario "${scenarioName}" not found`)
      return
    }
    
    const result = QuizTester.testScenario(scenario)
    const report = QuizTester.generateDetailedReport(result)
    console.log(report)
  } catch (error) {
    console.error(`Error testing scenario "${scenarioName}":`, error)
  }
}

/**
 * Helper function to quickly test all scenarios and log results
 */
export function quickTestAllScenarios(): void {
  try {
    const summary = QuizTester.testAllScenarios()
    const report = QuizTester.generateSummaryReport(summary)
    console.log(report)
    
    // Also log detailed reports for failed scenarios
    summary.results.forEach(result => {
      if (!result.conditionsMatched || result.totalRecommendations === 0) {
        const detailedReport = QuizTester.generateDetailedReport(result)
        console.log(detailedReport)
      }
    })
  } catch (error) {
    console.error('Error running quiz tests:', error)
  }
}