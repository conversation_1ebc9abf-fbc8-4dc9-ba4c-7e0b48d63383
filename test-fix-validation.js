/**
 * Quick Test to Validate the Wellness Optimization Bug Fix
 * This test checks that only recommendations for identified conditions are returned
 */

// Copy the processQuizAnswers function from updated-edge-function.js
// (In a real setup, you'd import it properly)

// Simulate the test by directly calling the edge function logic
console.log('🧪 Testing Wellness Optimization Bug Fix');
console.log('=========================================\n');

// Test 1: Sleep Quality Issues Only
const sleepOnlyAnswers = {
  '550e8400-e29b-41d4-a716-446655440003': 'Yes', // energy level - good
  '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes', // sugar cravings - no issues
  '550e8400-e29b-41d4-a716-446655440004': 'No',  // sleep trouble - YES, triggers sleep condition
  '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'Yes', // sleep rested - fine
  '550e8400-e29b-41d4-a716-446655440005': 'No',  // joint pain - no
  'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes', // joint flexible - good
  '550e8400-e29b-41d4-a716-446655440006': 'Yes', // exercise freq - good
  'ce586653-1155-4563-839f-266623795bae': 'Yes', // physical strong - good
  '550e8400-e29b-41d4-a716-446655440008': 'Yes', // stress - fine
  '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes', // work life balance - good
  '550e8400-e29b-41d4-a716-446655440009': 'Yes', // digestive health - good
  '33ddc48a-3741-428b-b877-173b0168ebf9': 'Yes', // concentration - good
  '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Yes', // immunity - good
  'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes', // nutrition - good
  'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes', // hydration - good
  'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes', // hair health - good
  'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes', // breathing - good
  'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes', // caffeine use - fine
  '550e8400-e29b-41d4-a716-446655440011': 'Yes', // cardio health 1 - good
  '550e8400-e29b-41d4-a716-446655440012': 'Yes', // cardio health 4 - good
  '550e8400-e29b-41d4-a716-446655440013': 'Yes', // weight mgmt 1 - good
  '550e8400-e29b-41d4-a716-446655440014': 'Yes'  // weight mgmt 3 - good
};

console.log('Test 1: Sleep Quality Issues Only');
console.log('Input: Only sleep trouble = "No" (all other answers = "Yes")');
console.log('Expected: Only Sleep Quality Issues condition identified');
console.log('Expected: Only Magnesium Glycinate + Tart Cherry Juice recommended');
console.log('Expected: NO B-Complex, Omega-3, or Ashwagandha\n');

// Instructions for manual testing:
console.log('📋 Manual Testing Instructions:');
console.log('1. Use the sleepOnlyAnswers object above in your quiz');
console.log('2. Check the API response in browser console');
console.log('3. Verify identifiedHealthTags contains only "Sleep Quality Issues"');
console.log('4. Verify results array contains exactly 2 items:');
console.log('   - Magnesium Glycinate (supplement)'); 
console.log('   - Tart Cherry Juice (food)');
console.log('5. Verify NO other supplements are included\n');

// Test 2: Multiple Health Tags
const multipleConditionsAnswers = {
  '550e8400-e29b-41d4-a716-446655440003': 'No',  // energy level - triggers Low Energy
  '550e8400-e29b-41d4-a716-446655440004': 'No',  // sleep trouble - triggers Sleep Quality
  '33ddc48a-3741-428b-b877-173b0168ebf9': 'No',  // concentration - triggers Brain Fog
  // All others 'Yes'
  '452ac791-288b-48aa-98ab-80d2173b2240': 'Yes',
  '598022d6-cece-4d65-a457-dcfe80a3a1fb': 'Yes',
  '550e8400-e29b-41d4-a716-446655440005': 'No',
  'b941ea42-0943-49e1-95a3-462f3debcc03': 'Yes',
  '550e8400-e29b-41d4-a716-446655440006': 'Yes',
  'ce586653-1155-4563-839f-266623795bae': 'Yes',
  '550e8400-e29b-41d4-a716-446655440008': 'Yes',
  '5b3879a5-e825-4fff-b786-b7bc1b4cc025': 'Yes',
  '550e8400-e29b-41d4-a716-446655440009': 'Yes',
  '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc': 'Yes',
  'b2254912-7bb7-4428-a365-b7c0de7c8bf5': 'Yes',
  'f232fa4c-4268-4d41-8e67-e0b71d67c4bd': 'Yes',
  'aeda2a7e-b897-4bc0-a67f-20f1306c83d0': 'Yes',
  'e2715890-51c7-4582-a89e-5007c3efb634': 'Yes',
  'c19fca54-eef1-460c-9c2a-abb5753f39f6': 'Yes',
  '550e8400-e29b-41d4-a716-446655440011': 'Yes',
  '550e8400-e29b-41d4-a716-446655440012': 'Yes',
  '550e8400-e29b-41d4-a716-446655440013': 'Yes',
  '550e8400-e29b-41d4-a716-446655440014': 'Yes'
};

console.log('Test 2: Multiple Health Tags (Sleep + Low Energy + Brain Fog)');
console.log('Expected: 3 health conditions identified');
console.log('Expected: Multi-condition supplements preferred (B-Complex covers Low Energy + Brain Fog)');
console.log('Expected: Efficient coverage with 4-6 recommendations total\n');

console.log('🔧 Fix Applied:');
console.log('✅ Removed wellness optimization logic (lines 1003-1079)');
console.log('✅ Only recommendations for identified conditions will be returned');
console.log('✅ No more supplements for unidentified health areas');

console.log('\n💡 To test the fix:');
console.log('1. Deploy the updated edge function');
console.log('2. Take the quiz with sleep-only answers');
console.log('3. Verify you get exactly 2 recommendations');
console.log('4. Verify no B-Complex, Omega-3, or Ashwagandha are included');

// Validation function you can call in browser console
function validateResults(apiResponse) {
  console.log('\n🔍 Validating API Response:');
  
  const { identifiedHealthTags, results } = apiResponse;
  
  console.log(`Health Tags Identified: ${identifiedHealthTags.length}`);
  identifiedHealthTags.forEach(tag => console.log(`  - ${tag.name}`));
  
  console.log(`\nRecommendations: ${results.length}`);
  results.forEach(result => {
    console.log(`  - ${result.recommendation_name} (for ${result.health_tag_name})`);
  });
  
  // Check for wellness optimization bug
  const identifiedTagNames = identifiedHealthTags.map(tag => tag.name);
  const invalidRecommendations = results.filter(result => 
    !identifiedTagNames.includes(result.health_tag_name)
  );
  
  if (invalidRecommendations.length === 0) {
    console.log('\n✅ VALIDATION PASSED: All recommendations are for identified conditions');
  } else {
    console.log('\n❌ VALIDATION FAILED: Found recommendations for unidentified conditions:');
    invalidRecommendations.forEach(rec => 
      console.log(`  - ${rec.recommendation_name} for ${rec.health_tag_name}`)
    );
  }
  
  return invalidRecommendations.length === 0;
}

console.log('\n🧪 Copy this function to browser console to validate:');
console.log('validateResults(apiResponse);');

export { sleepOnlyAnswers, multipleConditionsAnswers, validateResults };