import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { Resend } from "https://esm.sh/resend@2.0.0"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailRequest {
  to: string
  reportData: {
    healthTags: Array<{
      name: string
      description: string
    }>
    supplements: Array<{
      name: string
      description: string
      benefits: string[]
      dosage_info?: {
        min_dose?: string
        max_dose?: string
        timing?: string
        form?: string
        with_food?: boolean
      }
      side_effects?: string[]
      contraindications?: string[]
      interactions?: string[]
      pregnancy_safe?: boolean
      breastfeeding_safe?: boolean
      rationale?: string
      priority?: number
      score?: number
      condition_count?: number
      all_conditions?: string[]
    }>
    foods: Array<{
      name: string
      description: string
      benefits: string[]
      serving_suggestions?: string[]
      nutritional_info?: any
    }>
    currentDate: string
  }
}

function generateEmailHTML(reportData: EmailRequest['reportData']): string {
  const { healthTags, supplements, foods, currentDate } = reportData
  
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Personalized Health Report</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #374151;
          background-color: #f9fafb;
        }
        
        .container {
          max-width: 800px;
          margin: 0 auto;
          background-color: #ffffff;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
          background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
          color: white;
          padding: 40px 30px;
          text-align: center;
        }
        
        .logo {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin-bottom: 20px;
        }
        
        .logo-icon {
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #10b981 0%, #14b8a6 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .logo-icon::after {
          content: '';
          width: 16px;
          height: 16px;
          background-color: white;
          border-radius: 4px;
        }
        
        .logo-text {
          font-size: 24px;
          font-weight: 600;
          color: #10b981;
        }
        
        .header h1 {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 8px;
        }
        
        .header p {
          font-size: 18px;
          opacity: 0.9;
        }
        
        .content {
          padding: 40px 30px;
        }
        
        .section {
          margin-bottom: 40px;
        }
        
        .section-title {
          font-size: 24px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 2px solid #e5e7eb;
        }
        
        .subsection-title {
          font-size: 20px;
          font-weight: 600;
          color: #374151;
          margin: 24px 0 12px 0;
        }
        
        .summary {
          background-color: #f0f9ff;
          border-left: 4px solid #3b82f6;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 30px;
        }
        
        .health-tag {
          background-color: #fef3c7;
          border-left: 4px solid #f59e0b;
          padding: 16px;
          margin-bottom: 12px;
          border-radius: 6px;
        }
        
        .health-tag h3 {
          font-size: 18px;
          font-weight: 600;
          color: #92400e;
          margin-bottom: 4px;
        }
        
        .health-tag p {
          color: #78350f;
        }
        
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }
        
        .stat-card {
          background-color: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
        }
        
        .stat-number {
          font-size: 32px;
          font-weight: bold;
          color: #3b82f6;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #64748b;
        }
        
        .recommendation {
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 24px;
          margin-bottom: 20px;
        }
        
        .recommendation h3 {
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 8px;
        }
        
        .recommendation p {
          margin-bottom: 12px;
        }
        
        .benefits-list {
          list-style: none;
          margin: 12px 0;
        }
        
        .benefits-list li {
          padding: 4px 0;
          padding-left: 20px;
          position: relative;
        }
        
        .benefits-list li::before {
          content: '✓';
          position: absolute;
          left: 0;
          color: #10b981;
          font-weight: bold;
        }
        
        .dosage-info {
          background-color: #ecfdf5;
          border: 1px solid #d1fae5;
          border-radius: 6px;
          padding: 12px;
          margin: 12px 0;
        }
        
        .priority-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        
        .priority-high {
          background-color: #fee2e2;
          color: #991b1b;
        }
        
        .priority-medium {
          background-color: #fef3c7;
          color: #92400e;
        }
        
        .priority-low {
          background-color: #dcfce7;
          color: #166534;
        }
        
        .warning {
          background-color: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 6px;
          padding: 12px;
          margin: 12px 0;
        }
        
        .warning-title {
          font-weight: 600;
          color: #dc2626;
          margin-bottom: 4px;
        }
        
        .safety-section {
          background-color: #fffbeb;
          border: 2px solid #fbbf24;
          border-radius: 8px;
          padding: 24px;
          margin-top: 40px;
        }
        
        .safety-title {
          font-size: 20px;
          font-weight: bold;
          color: #92400e;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .footer {
          background-color: #f3f4f6;
          padding: 30px;
          text-align: center;
          border-top: 1px solid #e5e7eb;
        }
        
        .footer p {
          color: #6b7280;
          font-size: 14px;
          margin-bottom: 8px;
        }
        
        @media (max-width: 600px) {
          .container {
            margin: 0;
            box-shadow: none;
          }
          
          .header, .content, .footer {
            padding: 20px;
          }
          
          .stats-grid {
            grid-template-columns: 1fr;
          }
          
          .header h1 {
            font-size: 24px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <!-- Header with Logo -->
        <div class="header">
          <div class="logo">
            <div class="logo-icon"></div>
            <span class="logo-text">LifeSupplier</span>
          </div>
          <h1>Your Personalized Health Report</h1>
          <p>Generated on ${currentDate}</p>
        </div>
        
        <!-- Content -->
        <div class="content">
          <!-- Executive Summary -->
          <div class="section">
            <div class="summary">
              <h2 class="section-title">Executive Summary</h2>
              <p>
                ${healthTags.length > 0 
                  ? `Based on your quiz responses, we identified ${healthTags.length} key health area${healthTags.length > 1 ? 's' : ''} that could benefit from targeted nutritional support. This report provides personalized supplement and food recommendations to help you optimize your health and wellbeing.`
                  : 'Based on your responses, you have a solid health foundation. This report provides general wellness recommendations to help you maintain and optimize your health.'
                }
              </p>
            </div>
          </div>
          
          <!-- Quick Stats -->
          <div class="section">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">${supplements.length}</div>
                <div class="stat-label">Supplement Recommendations</div>
              </div>
              <div class="stat-card" style="color: #059669;">
                <div class="stat-number" style="color: #059669;">${foods.length}</div>
                <div class="stat-label">Food Recommendations</div>
              </div>
              <div class="stat-card" style="color: #ea580c;">
                <div class="stat-number" style="color: #ea580c;">${healthTags.length}</div>
                <div class="stat-label">Health Areas</div>
              </div>
            </div>
          </div>
          
          ${healthTags.length > 0 ? `
          <!-- Health Areas -->
          <div class="section">
            <h2 class="section-title">Health Areas to Focus On</h2>
            ${healthTags.map((tag, index) => `
              <div class="health-tag">
                <h3>${index + 1}. ${tag.name}</h3>
                <p>${tag.description}</p>
              </div>
            `).join('')}
          </div>
          ` : ''}
          
          ${supplements.length > 0 ? `
          <!-- Supplement Recommendations -->
          <div class="section">
            <h2 class="section-title">🧬 Your Supplement Recommendations</h2>
            <div class="warning">
              <div class="warning-title">⚠️ Start Smart: One Supplement at a Time</div>
              <p>Begin with ONE supplement only. Wait 5-7 days before adding another. This helps you identify what works and avoid adverse reactions.</p>
            </div>
            
            ${supplements.map((supplement, index) => {
              const priorityClass = supplement.score && supplement.score > 7 ? 'priority-high' : 
                                   supplement.score && supplement.score > 4 ? 'priority-medium' : 'priority-low';
              const priorityText = supplement.score && supplement.score > 7 ? 'HIGH PRIORITY' : 
                                   supplement.score && supplement.score > 4 ? 'MEDIUM PRIORITY' : 'LOW PRIORITY';
              
              return `
                <div class="recommendation">
                  ${supplement.score ? `<span class="priority-badge ${priorityClass}">${priorityText}</span>` : ''}
                  <h3>${index + 1}. ${supplement.name}</h3>
                  <p><strong>Description:</strong> ${supplement.description}</p>
                  
                  ${supplement.benefits && supplement.benefits.length > 0 ? `
                    <div>
                      <strong>Benefits:</strong>
                      <ul class="benefits-list">
                        ${supplement.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
                      </ul>
                    </div>
                  ` : ''}
                  
                  ${supplement.dosage_info ? `
                    <div class="dosage-info">
                      <strong>💊 Dosage Information:</strong><br>
                      ${supplement.dosage_info.min_dose ? `<strong>Dose:</strong> ${supplement.dosage_info.min_dose}<br>` : ''}
                      ${supplement.dosage_info.timing ? `<strong>Timing:</strong> ${supplement.dosage_info.timing}<br>` : ''}
                      ${supplement.dosage_info.with_food ? `<strong>Take with food:</strong> Yes<br>` : ''}
                      ${supplement.dosage_info.form ? `<strong>Form:</strong> ${supplement.dosage_info.form}` : ''}
                    </div>
                  ` : ''}
                  
                  ${supplement.rationale ? `<p><strong>Why this supplement:</strong> ${supplement.rationale}</p>` : ''}
                  
                  ${supplement.condition_count && supplement.condition_count > 1 ? `
                    <p><strong>🎯 Multi-Benefit:</strong> Addresses ${supplement.condition_count} of your health areas${supplement.all_conditions ? ': ' + supplement.all_conditions.join(', ') : ''}</p>
                  ` : ''}
                  
                  ${(supplement.pregnancy_safe === false || supplement.breastfeeding_safe === false) ? `
                    <div class="warning">
                      <div class="warning-title">⚠️ Safety Warning</div>
                      <p>
                        ${supplement.pregnancy_safe === false ? 'Not safe during pregnancy' : ''}
                        ${supplement.pregnancy_safe === false && supplement.breastfeeding_safe === false ? ' • ' : ''}
                        ${supplement.breastfeeding_safe === false ? 'Not safe while breastfeeding' : ''}
                      </p>
                    </div>
                  ` : ''}
                  
                  ${supplement.side_effects && supplement.side_effects.length > 0 ? `
                    <p><strong>⚠️ Possible Side Effects:</strong> ${supplement.side_effects.join(', ')}</p>
                  ` : ''}
                  
                  ${supplement.interactions && supplement.interactions.length > 0 ? `
                    <p><strong>🔄 Drug Interactions:</strong> ${supplement.interactions.join(', ')}</p>
                  ` : ''}
                  
                  ${supplement.contraindications && supplement.contraindications.length > 0 ? `
                    <p><strong>🚫 Contraindications:</strong> ${supplement.contraindications.join(', ')}</p>
                  ` : ''}
                </div>
              `;
            }).join('')}
          </div>
          ` : ''}
          
          ${foods.length > 0 ? `
          <!-- Food Recommendations -->
          <div class="section">
            <h2 class="section-title">🥗 Nourishing Food Recommendations</h2>
            ${foods.map((food, index) => `
              <div class="recommendation">
                <h3>${index + 1}. ${food.name}</h3>
                <p><strong>Description:</strong> ${food.description}</p>
                
                ${food.benefits && food.benefits.length > 0 ? `
                  <div>
                    <strong>Benefits:</strong>
                    <ul class="benefits-list">
                      ${food.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
                    </ul>
                  </div>
                ` : ''}
                
                ${food.serving_suggestions && food.serving_suggestions.length > 0 ? `
                  <div>
                    <strong>🍽️ Serving Suggestions:</strong>
                    <ul class="benefits-list">
                      ${food.serving_suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                  </div>
                ` : ''}
              </div>
            `).join('')}
          </div>
          ` : ''}
          
          <!-- Safety Information -->
          <div class="safety-section">
            <div class="safety-title">
              🛡️ Safety First: Important Guidelines
            </div>
            <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
              <div style="font-weight: 600; color: #dc2626; margin-bottom: 8px;">⚠️ ALWAYS consult your healthcare provider before starting any supplement regimen, especially if you take medications or have health conditions.</div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
              <div>
                <h4 style="font-weight: 600; margin-bottom: 12px; color: #374151;">🚨 Stop and Call Your Doctor If You Experience:</h4>
                <ul style="list-style: none; padding: 0;">
                  <li style="padding: 2px 0;">• Severe nausea or vomiting</li>
                  <li style="padding: 2px 0;">• Unusual heart palpitations</li>
                  <li style="padding: 2px 0;">• Persistent headaches</li>
                  <li style="padding: 2px 0;">• Skin rashes or allergic reactions</li>
                  <li style="padding: 2px 0;">• Severe digestive issues</li>
                </ul>
              </div>
              
              <div>
                <h4 style="font-weight: 600; margin-bottom: 12px; color: #374151;">✅ Best Practices:</h4>
                <ul style="list-style: none; padding: 0;">
                  <li style="padding: 2px 0;">• Start with ONE supplement at a time</li>
                  <li style="padding: 2px 0;">• Keep a daily health journal</li>
                  <li style="padding: 2px 0;">• Take photos of supplement labels</li>
                  <li style="padding: 2px 0;">• Set reminders for consistent timing</li>
                  <li style="padding: 2px 0;">• Buy from reputable manufacturers</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
          <p><strong>LifeSupplier</strong> - Your Personalized Health Companion</p>
          <p>This report is for informational purposes only and does not replace professional medical advice.</p>
          <p style="margin-top: 16px; font-style: italic;">Remember: Start with ONE supplement, monitor effects, consult healthcare providers</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { to, reportData }: EmailRequest = await req.json()

    if (!to || !reportData) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: to, reportData' }), 
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Initialize Resend with API key
    const resend = new Resend('re_Dofzut3H_6mFVaqm6TsQZ2tt3VAXt3kxc')

    // Generate HTML email content
    const htmlContent = generateEmailHTML(reportData)

    // Send email
    const emailResponse = await resend.emails.send({
      from: 'Sarah Supplier <<EMAIL>>',
      to: [to],
      subject: '🧬 Your Personalized Health Report from LifeSupplier',
      html: htmlContent,
    })

    console.log('Email sent successfully:', emailResponse)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Health report sent successfully',
        emailId: emailResponse.data?.id 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error sending email:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to send email', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
