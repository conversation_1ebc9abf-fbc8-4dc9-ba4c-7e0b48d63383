import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Activity, Brain, Pill, FileText } from 'lucide-react'

interface AnalysisProps {
  email: string
  answers: Record<string, any>
  onComplete: () => void
}

interface ProgressStep {
  icon: React.ReactNode
  title: string
  subtitle: string
  duration: number
}

export function Analysis({ email, answers, onComplete }: AnalysisProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)
  const [stepProgress, setStepProgress] = useState(0)

  const steps: ProgressStep[] = [
    {
      icon: <Activity className="w-6 h-6" />,
      title: "Analyzing your health data",
      subtitle: "Processing your responses and identifying patterns",
      duration: 4000
    },
    {
      icon: <Brain className="w-6 h-6" />,
      title: "Processing biomarker compatibility",
      subtitle: "Evaluating nutritional needs and genetic factors",
      duration: 4000
    },
    {
      icon: <Pill className="w-6 h-6" />,
      title: "Generating supplement recommendations",
      subtitle: "Matching supplements to your specific health profile",
      duration: 4000
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Creating your personalized report",
      subtitle: "Compiling insights and 30-day action plans",
      duration: 3000
    }
  ]

  useEffect(() => {
    if (currentStep >= steps.length) {
      setTimeout(() => {
        onComplete()
      }, 500)
      return
    }

    const currentStepData = steps[currentStep]
    const startTime = Date.now()

    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime
      const stepProgress = Math.min((elapsed / currentStepData.duration) * 100, 100)
      setStepProgress(stepProgress)

      // Calculate overall progress
      const completedSteps = currentStep
      const currentStepContribution = stepProgress / steps.length
      const totalProgress = (completedSteps / steps.length) * 100 + currentStepContribution
      setProgress(totalProgress)

      if (elapsed >= currentStepData.duration) {
        clearInterval(interval)
        setCurrentStep(prev => prev + 1)
        setStepProgress(0)
      }
    }, 50)

    return () => clearInterval(interval)
  }, [currentStep, onComplete])

  const currentStepData = steps[currentStep] || steps[steps.length - 1]

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50">
      {/* Header with Logo */}
      <div className="w-full px-4 py-6">
        <div className="flex items-center gap-2 max-w-2xl mx-auto">
          <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
            <div className="w-4 h-4 bg-white rounded-sm"></div>
          </div>
          <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex items-center justify-center px-4 pb-8">
        <div className="w-full max-w-2xl">
        <Card className="shadow-2xl bg-white/95 backdrop-blur-sm border border-emerald-200/50">
          <CardContent className="p-8 md:p-12">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-3">
                Analyzing Your Health Profile
              </h1>
              <p className="text-lg text-gray-600">
                Our AI is processing your responses to create personalized recommendations
              </p>
            </div>

            {/* Main Progress Indicator */}
            <div className="mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-4 bg-emerald-100 rounded-full text-emerald-600">
                  {currentStepData.icon}
                </div>
              </div>
              
              <h3 className="text-xl font-semibold text-center text-gray-800 mb-2">
                {currentStepData.title}
              </h3>
              <p className="text-center text-gray-600 mb-6">
                {currentStepData.subtitle}
              </p>

              {/* Overall Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-3 mb-6">
                <div
                  className="bg-gradient-to-r from-emerald-500 to-teal-500 h-3 rounded-full transition-all duration-200 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>

              {/* Current Step Progress */}
              <div className="text-center text-sm text-gray-500 mb-6">
                Step {currentStep + 1} of {steps.length} - {Math.round(stepProgress)}% complete
              </div>
            </div>

            {/* Step Indicators */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className={`text-center p-3 rounded-lg transition-all duration-300 ${
                    index < currentStep
                      ? 'bg-emerald-100 text-emerald-700'
                      : index === currentStep
                      ? 'bg-emerald-50 text-emerald-600 border-2 border-emerald-200'
                      : 'bg-gray-50 text-gray-400'
                  }`}
                >
                  <div className="flex justify-center mb-2">
                    {step.icon}
                  </div>
                  <div className="text-xs font-medium leading-tight">
                    {index === 0 && "Analyzing health"}
                    {index === 1 && "Processing biomarker"}
                    {index === 2 && "Generating supplements"}
                    {index === 3 && "Creating report"}
                  </div>
                </div>
              ))}
            </div>

            {/* User Info */}
            <div className="text-center text-sm text-gray-500 bg-gray-50 rounded-lg p-4">
              <p className="mb-1">Analyzing data for: <span className="font-medium text-gray-700">{email}</span></p>
              <p>Processing {Object.keys(answers).length} health assessment responses</p>
            </div>

            {/* Fun Facts */}
            <div className="mt-6 text-center">
              <div className="text-sm text-emerald-600 font-medium">
                {currentStep === 0 && "🔍 Identifying 47+ health markers from your responses"}
                {currentStep === 1 && "🧬 Cross-referencing with 2,000+ clinical studies"}
                {currentStep === 2 && "💊 Evaluating 500+ supplement combinations"}
                {currentStep === 3 && "📋 Generating your personalized 30-day protocol"}
                {currentStep >= 4 && "✨ Your personalized health report is ready!"}
              </div>
            </div>
          </CardContent>
        </Card>
        </div>
      </div>
    </div>
  )
}