// Practical Examples of Weighted Random Selection 
// Using your actual supplement data from updated-edge-function.js

// Your current supplements with overlapping health tags
const CURRENT_SUPPLEMENTS = [
  // The redundancy problem supplements
  { name: "Ceylon Cinnamon", score: 140, conditions: ["Low Energy & Blood Sugar", "Weight Management Support"] },
  { name: "Chromium Picolinate", score: 140, conditions: ["Low Energy & Blood Sugar", "Weight Management Support"] },
  
  // Other high-scoring supplements
  { name: "B-Complex Vitamins", score: 140, conditions: ["Low Energy & Blood Sugar", "Brain Fog & Focus"] },
  { name: "Omega-3 Fish Oil", score: 140, conditions: ["Joint Support Needed", "Brain Fog & Focus"] },
  
  // Medium-scoring supplements
  { name: "Vitamin D3", score: 120, conditions: ["Immune Support", "Stress & Lifestyle Balance"] },
  { name: "Magnesium Glycinate", score: 120, conditions: ["Sleep Quality Issues", "Physical Performance"] },
  { name: "Ashwagandha", score: 120, conditions: ["Stress & Lifestyle Balance", "Physical Performance"] },
  
  // Single-condition supplements  
  { name: "L-Theanine", score: 100, conditions: ["Caffeine Dependency"] },
  { name: "Coenzyme Q10", score: 100, conditions: ["Sedentary Lifestyle"] }
];

// Weighted Random Selection Function
function weightedRandomSelection(supplements) {
  if (!supplements || supplements.length === 0) return null;
  if (supplements.length === 1) return supplements[0];
  
  const totalWeight = supplements.reduce((sum, item) => sum + item.score, 0);
  let random = Math.random() * totalWeight;
  
  for (const item of supplements) {
    random -= item.score;
    if (random <= 0) return item;
  }
  return supplements[supplements.length - 1];
}

// Current Algorithm (Greedy - Always picks first/highest)
function currentGreedySelection(supplements) {
  if (!supplements || supplements.length === 0) return null;
  return supplements.sort((a, b) => b.score - a.score)[0]; // Always returns highest scoring
}

console.log("WEIGHTED RANDOM SELECTION - PRACTICAL EXAMPLES");
console.log("=".repeat(60));

// EXAMPLE 1: The Redundancy Problem
console.log("\n📊 EXAMPLE 1: SOLVING THE REDUNDANCY PROBLEM");
console.log("=".repeat(60));
console.log("User has: Low Energy & Blood Sugar + Weight Management Support");
console.log("Available supplements:");

const redundantSupplements = CURRENT_SUPPLEMENTS.filter(s => 
  s.conditions.includes("Low Energy & Blood Sugar") || 
  s.conditions.includes("Weight Management Support")
);

redundantSupplements.forEach(s => {
  console.log(`  ${s.name} (Score: ${s.score}) - ${s.conditions.join(", ")}`);
});

console.log("\n🔴 CURRENT ALGORITHM (Greedy):");
console.log("Always selects the same supplements in same order:");
for (let i = 0; i < 5; i++) {
  const selected = currentGreedySelection(redundantSupplements);
  console.log(`  Run ${i+1}: ${selected.name}`);
}

console.log("\n🟢 NEW ALGORITHM (Weighted Random):");
console.log("Provides variety while favoring high-scoring supplements:");
const selections = {};
for (let i = 0; i < 5; i++) {
  const selected = weightedRandomSelection(redundantSupplements);
  selections[selected.name] = (selections[selected.name] || 0) + 1;
  console.log(`  Run ${i+1}: ${selected.name}`);
}

console.log("\n📈 Distribution Analysis (1000 selections):");
const distributionResults = {};
for (let i = 0; i < 1000; i++) {
  const selected = weightedRandomSelection(redundantSupplements);
  distributionResults[selected.name] = (distributionResults[selected.name] || 0) + 1;
}

const totalWeight = redundantSupplements.reduce((sum, s) => sum + s.score, 0);
Object.entries(distributionResults)
  .sort((a, b) => b[1] - a[1])
  .forEach(([name, count]) => {
    const supplement = redundantSupplements.find(s => s.name === name);
    const actualPercent = (count / 1000 * 100).toFixed(1);
    const expectedPercent = (supplement.score / totalWeight * 100).toFixed(1);
    console.log(`  ${name}: ${count} times (${actualPercent}%) | Expected: ${expectedPercent}%`);
  });

// EXAMPLE 2: Realistic User Scenario
console.log("\n\n📊 EXAMPLE 2: REALISTIC USER SCENARIO");
console.log("=".repeat(60));
console.log("User Profile: Sarah, 32, has multiple health concerns");
console.log("Identified conditions: Low Energy, Brain Fog, Stress, Sleep Issues");

const sarahsConditions = ["Low Energy & Blood Sugar", "Brain Fog & Focus", "Stress & Lifestyle Balance", "Sleep Quality Issues"];
const availableForSarah = CURRENT_SUPPLEMENTS.filter(s => 
  s.conditions.some(condition => sarahsConditions.includes(condition))
);

console.log("\nAvailable supplements for Sarah:");
availableForSarah.forEach(s => {
  const matchingConditions = s.conditions.filter(c => sarahsConditions.includes(c));
  console.log(`  ${s.name} (Score: ${s.score}) - Matches: ${matchingConditions.join(", ")}`);
});

console.log("\n🔴 CURRENT ALGORITHM - Same Result Every Time:");
const currentResults = [];
for (let i = 0; i < 3; i++) {
  // Simulate current greedy selection of top 3
  const sorted = [...availableForSarah].sort((a, b) => b.score - a.score);
  currentResults.push(sorted.slice(0, 3).map(s => s.name));
}
currentResults.forEach((result, i) => {
  console.log(`  Visit ${i+1}: ${result.join(", ")}`);
});

console.log("\n🟢 NEW ALGORITHM - Variety Across Visits:");
for (let visit = 1; visit <= 3; visit++) {
  const visitResults = [];
  const selectedNames = new Set();
  
  // Select 3 supplements for this visit
  let attempts = 0;
  while (visitResults.length < 3 && attempts < 10) {
    const selected = weightedRandomSelection(availableForSarah);
    if (!selectedNames.has(selected.name)) {
      visitResults.push(selected.name);
      selectedNames.add(selected.name);
    }
    attempts++;
  }
  
  console.log(`  Visit ${visit}: ${visitResults.join(", ")}`);
}

console.log("\n📊 Sarah's Selection Distribution (500 simulations):");
const sarahDistribution = {};
for (let i = 0; i < 500; i++) {
  const selected = weightedRandomSelection(availableForSarah);
  sarahDistribution[selected.name] = (sarahDistribution[selected.name] || 0) + 1;
}

const sarahTotalWeight = availableForSarah.reduce((sum, s) => sum + s.score, 0);
Object.entries(sarahDistribution)
  .sort((a, b) => b[1] - a[1])
  .forEach(([name, count]) => {
    const supplement = availableForSarah.find(s => s.name === name);
    const actualPercent = (count / 500 * 100).toFixed(1);
    const expectedPercent = (supplement.score / sarahTotalWeight * 100).toFixed(1);
    const deviation = Math.abs(parseFloat(actualPercent) - parseFloat(expectedPercent)).toFixed(1);
    console.log(`  ${name.padEnd(20)} | ${count.toString().padEnd(3)} (${actualPercent}%) | Expected: ${expectedPercent}% | ±${deviation}%`);
  });

console.log("\n" + "=".repeat(60));
console.log("🎯 KEY BENEFITS DEMONSTRATED:");
console.log("=".repeat(60));
console.log("✅ Redundancy Reduced: Ceylon Cinnamon & Chromium not always both selected");
console.log("✅ Variety Added: Users get different valid combinations");
console.log("✅ Quality Maintained: Higher-scoring supplements still favored");
console.log("✅ User Experience: Prevents supplement fatigue from identical recommendations");
console.log("✅ Business Value: Reduces customer complaints about repetitive suggestions");

console.log("\n📈 STATISTICAL PROOF:");
console.log("• Higher scores = Higher selection probability (preserved expected value)");
console.log("• Natural variety without sacrificing recommendation quality");
console.log("• Perfect solution for your current redundancy issues");