import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { useQuestionsStatic } from '../hooks/useQuestionsStatic'
import { useQuizResults } from '../hooks/useQuizResults'
import { QUIZ_SCENARIOS } from '../test-data/predefinedQuizScenarios'

// Mock Supabase
vi.mock('../lib/supabase', () => ({
  supabase: {
    rpc: vi.fn(),
  },
}))

describe('React Hooks', () => {
  describe('useQuestionsStatic', () => {
    it('should return questions without loading', () => {
      const { result } = renderHook(() => useQuestionsStatic())
      
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.questions).toBeDefined()
      expect(Array.isArray(result.current.questions)).toBe(true)
      expect(result.current.questions.length).toBeGreaterThan(0)
    })

    it('should return questions with expected structure', () => {
      const { result } = renderHook(() => useQuestionsStatic())
      
      result.current.questions.forEach((question: any) => {
        expect(question.id).toBeDefined()
        expect(question.text).toBeDefined()
        expect(question.type).toBeDefined()
        expect(Array.isArray(question.options)).toBe(true)
        expect(question.options.length).toBeGreaterThan(0)
        expect(typeof question.order).toBe('number')
      })
    })

    it('should return questions sorted by order', () => {
      const { result } = renderHook(() => useQuestionsStatic())
      
      const questions = result.current.questions
      for (let i = 0; i < questions.length - 1; i++) {
        expect(questions[i].order <= questions[i + 1].order).toBe(true)
      }
    })

    it('should include specific question IDs used in test scenarios', () => {
      const { result } = renderHook(() => useQuestionsStatic())
      
      const questionIds = result.current.questions.map((q: any) => q.id)
      
      // Check for some key question IDs used in our test scenarios
      expect(questionIds).toContain('550e8400-e29b-41d4-a716-446655440003') // energy levels
      expect(questionIds).toContain('550e8400-e29b-41d4-a716-446655440004') // sleep
      expect(questionIds).toContain('550e8400-e29b-41d4-a716-446655440005') // joints
      expect(questionIds).toContain('550e8400-e29b-41d4-a716-446655440008') // stress
    })
  })

  describe('useQuizResults', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should initialize with default state', () => {
      const { result } = renderHook(() => useQuizResults())
      
      expect(result.current.processing).toBe(false)
      expect(result.current.error).toBeNull()
      expect(typeof result.current.processResults).toBe('function')
    })

    it('should handle successful quiz processing', async () => {
      const mockResults = [
        {
          primary_health_tag: 'Low Energy',
          primary_health_description: 'Test description',
          recommendation_type: 'supplement',
          recommendation_id: 'test-id',
          recommendation_name: 'Test Supplement',
          recommendation_details: {
            description: 'Test supplement description',
            benefits: ['Test benefit'],
            dosage_info: {
              timing: 'Morning',
              with_food: true
            }
          },
          condition_count: 1,
          all_conditions: ['Low Energy'],
          priority_score: 100
        }
      ]

      const { supabase } = await import('../lib/supabase')
      vi.mocked(supabase.rpc).mockResolvedValue({ data: mockResults, error: null })

      const { result } = renderHook(() => useQuizResults())
      
      const healthyScenario = QUIZ_SCENARIOS.find(s => s.name === 'Healthy Person')!
      
      let processResults: any
      await waitFor(async () => {
        processResults = await result.current.processResults(healthyScenario.answers)
        expect(processResults).toBeDefined()
      })

      expect(result.current.processing).toBe(false)
      expect(result.current.error).toBeNull()
      expect(processResults).toHaveLength(1)
      expect(processResults[0].health_tag_name).toBe('Low Energy')
      expect(processResults[0].recommendation_type).toBe('supplement')
    })

    it('should handle processing errors', async () => {
      const mockError = new Error('Database error')
      
      const { supabase } = await import('../lib/supabase')
      vi.mocked(supabase.rpc).mockResolvedValue({ data: null, error: mockError })

      const { result } = renderHook(() => useQuizResults())
      
      const healthyScenario = QUIZ_SCENARIOS.find(s => s.name === 'Healthy Person')!
      
      await expect(result.current.processResults(healthyScenario.answers)).rejects.toThrow()
      
      await waitFor(() => {
        expect(result.current.processing).toBe(false)
        expect(result.current.error).not.toBeNull()
      })
    })

    it('should set processing state during execution', async () => {
      const mockResults: any[] = []
      let resolvePromise: (() => void) | undefined
      
      const { supabase } = await import('../lib/supabase')
      vi.mocked(supabase.rpc).mockImplementation(() => 
        new Promise(resolve => {
          resolvePromise = () => resolve({ data: mockResults, error: null })
        })
      )

      const { result } = renderHook(() => useQuizResults())
      
      const healthyScenario = QUIZ_SCENARIOS.find(s => s.name === 'Healthy Person')!
      
      const processPromise = result.current.processResults(healthyScenario.answers)
      
      // Wait a bit then check processing state
      await waitFor(() => {
        expect(result.current.processing).toBe(true)
      })
      
      // Resolve the promise
      resolvePromise!()
      await processPromise
      
      // Should no longer be processing
      await waitFor(() => {
        expect(result.current.processing).toBe(false)
      })
    })

    it('should convert answers to correct format for database', async () => {
      const mockResults: any[] = []
      
      const { supabase } = await import('../lib/supabase')
      vi.mocked(supabase.rpc).mockResolvedValue({ data: mockResults, error: null })

      const { result } = renderHook(() => useQuizResults())
      
      const testAnswers = {
        'question-1': 'answer-1',
        'question-2': 'answer-2'
      }
      
      await result.current.processResults(testAnswers)
      
      expect(supabase.rpc).toHaveBeenCalledWith('process_quiz_results_bridge_unique', {
        user_answers: testAnswers
      })
    })
  })

  describe('Integration Tests', () => {
    it('should process all quiz scenarios with questions hook', () => {
      const { result: questionsResult } = renderHook(() => useQuestionsStatic())
      const questions = questionsResult.current.questions
      
      QUIZ_SCENARIOS.forEach(scenario => {
        // Verify all question IDs in scenario exist in questions hook
        Object.keys(scenario.answers).forEach(questionId => {
          const question = questions.find((q: any) => q.id === questionId)
          if (question) {
            // Verify answer is valid for the question
            expect(question.options).toContain(scenario.answers[questionId])
          }
        })
      })
    })
  })
})