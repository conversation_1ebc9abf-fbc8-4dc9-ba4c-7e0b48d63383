// Quick debug test for process-quiz function
// You can test this locally to see if the issue is with the data processing

const testAnswers = {
  '550e8400-e29b-41d4-a716-446655440001': 'Female',
  '550e8400-e29b-41d4-a716-446655440002': 'Under 18',
  '550e8400-e29b-41d4-a716-446655440003': 'Yes',
  '550e8400-e29b-41d4-a716-446655440004': 'Yes'
};

console.log('Testing quiz processing...');
console.log('Input answers:', testAnswers);

// Simulate the basic logic
const identifiedConditions = new Set();

// Test condition identification
const energyLevel = testAnswers['550e8400-e29b-41d4-a716-446655440003'];
if (energyLevel === 'No') {
  identifiedConditions.add('Low Energy & Blood Sugar');
}

console.log('Identified conditions:', Array.from(identifiedConditions));
console.log('Processing completed successfully');