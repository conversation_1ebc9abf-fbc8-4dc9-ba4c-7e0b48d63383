import { useState, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { checkUserSubscription } from '@/utils/subscriptionUtils'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

interface SubscriptionGuardProps {
  children: React.ReactNode
  fallbackPath?: string
}

export function SubscriptionGuard({ children, fallbackPath = '/payment' }: SubscriptionGuardProps) {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false)

  useEffect(() => {
    const checkSubscription = async () => {
      if (!user) {
        console.log('🔒 SubscriptionGuard: No user, redirecting to home')
        setIsLoading(false)
        return
      }

      console.log('🔍 SubscriptionGuard: Checking subscription for user:', user.id)
      
      try {
        const subscriptionInfo = await checkUserSubscription(user.id)
        console.log('📊 SubscriptionGuard: Subscription check result:', subscriptionInfo)
        
        setHasActiveSubscription(subscriptionInfo.hasActiveSubscription)
        
        if (subscriptionInfo.hasActiveSubscription) {
          console.log('✅ SubscriptionGuard: User has active subscription, allowing access')
        } else {
          console.log('❌ SubscriptionGuard: User has no active subscription, will redirect to:', fallbackPath)
        }
        
      } catch (error) {
        console.error('💥 SubscriptionGuard: Error checking subscription:', error)
        setHasActiveSubscription(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkSubscription()
  }, [user, fallbackPath])

  // Show loading while checking subscription
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Verifying subscription...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Redirect if no user
  if (!user) {
    console.log('🔒 SubscriptionGuard: No user, redirecting to home')
    return <Navigate to="/" replace />
  }

  // Redirect if no active subscription
  if (!hasActiveSubscription) {
    console.log('❌ SubscriptionGuard: No subscription, redirecting to:', fallbackPath)
    return <Navigate to={fallbackPath} replace />
  }

  // User has active subscription, render protected content
  console.log('✅ SubscriptionGuard: Access granted')
  return <>{children}</>
}