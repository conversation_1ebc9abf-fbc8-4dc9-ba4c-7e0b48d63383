# Supabase CLI Setup Guide

## Option 1: Manual Setup (Recommended - Always Works)

### Go to your Supabase Dashboard:
https://supabase.com/dashboard/project/zjeqsipsrjeojscjnihe

### SQL Editor Steps:
1. Go to "SQL Editor" in left sidebar
2. Click "New Query"
3. Copy/paste `supabase/migrations/20240101000001_initial_schema.sql`
4. Click "Run"
5. Create another new query
6. Copy/paste `supabase/seed.sql` 
7. Click "Run"

✅ **This approach is guaranteed to work!**

---

## Option 2: CLI Setup (If you know your DB password)

If you remember the database password you set when creating your Supabase project:

### Step 1: Link to your project
```bash
npx supabase link --project-ref zjeqsipsrjeojscjnihe
```
(Enter your database password when prompted)

### Step 2: Push migrations
```bash
npx supabase db push
```

### Step 3: Reset with seed data
```bash
npx supabase db reset --seed
```

---

## Option 3: Reset Database Password (If you forgot)

1. Go to: https://supabase.com/dashboard/project/zjeqsipsrjeojscjnihe/settings/database
2. Click "Reset database password"
3. Set a new password
4. Use Option 2 above with the new password

---

## What Gets Created

After running either option, you'll have:

- ✅ **7 tables**: profiles, questions, health_tags, recommendations, responses, reports, subscriptions
- ✅ **10 quiz questions**: Gender, age, energy, sleep, joints, exercise, diet, stress, digestion, medications
- ✅ **6 health categories**: Low energy, sleep issues, joint support, physical activity, stress management, digestive health
- ✅ **12 supplements**: B-complex, iron, magnesium, melatonin, omega-3, turmeric, vitamin D3, creatine, ashwagandha, L-theanine, probiotics, digestive enzymes
- ✅ **10+ food recommendations**: Iron-rich foods, complex carbs, tart cherry juice, fatty fish, berries, protein foods, fermented foods, leafy greens, etc.
- ✅ **User authentication**: Login/signup with automatic profile creation
- ✅ **Admin capabilities**: Role-based permissions for content management
- ✅ **Security policies**: Row-level security protecting user data

## Verification

After setup, check these tables in your Supabase dashboard:
- `questions` should have 10 rows
- `health_tags` should have 6 rows  
- `recommendations` should have 20+ rows

Once complete, the app will automatically switch from static to database mode!