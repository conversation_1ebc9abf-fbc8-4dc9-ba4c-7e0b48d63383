import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useQuestionsStatic } from '@/hooks/useQuestionsStatic'
import { useCreateUserAndSendReset } from '@/hooks/useCreateUserAndSendReset'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2, Undo2, Eye, EyeOff, User } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { encodeQuizDataForURL, getQuizDataFromURL } from '@/utils/quizStorage'

interface QuizProps {
  onComplete: (answers: Record<string, any>, email?: string, options?: { skipToPayment?: boolean; checkPaymentStatus?: boolean }) => void
  isDirectPayment?: boolean
  isLoggedIn?: boolean
}


export function Quiz({ onComplete, isDirectPayment = false, isLoggedIn = false }: QuizProps) {
  const { questions, loading, error } = useQuestionsStatic()
  const { createUserAndSendReset } = useCreateUserAndSendReset()
  const { refreshAuthState } = useAuth()
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [cardKey, setCardKey] = useState(0)
  const [transitionDirection, setTransitionDirection] = useState<'out' | 'in' | 'entering' | 'out-reverse' | 'entering-reverse'>('in')
  const [email, setEmail] = useState('')
  const [showEmailInput, setShowEmailInput] = useState(false)
  const [isCreatingUser, setIsCreatingUser] = useState(false)
  const [userCreationError, setUserCreationError] = useState<string | null>(null)
  const [showSignInOption, setShowSignInOption] = useState(false)
  const [isSigningIn, setIsSigningIn] = useState(false)
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [signInIndicator, setSignInIndicator] = useState<string | null>(null)
  
  // Consent checkboxes
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [agreedToPrivacy, setAgreedToPrivacy] = useState(false)
  const [agreedToHealthData, setAgreedToHealthData] = useState(false)
  const [optInMarketing, setOptInMarketing] = useState(false)

  // Load previously saved answers from URL on component mount
  useEffect(() => {
    if (!isDirectPayment) {
      const quizDataResult = getQuizDataFromURL()
      if (quizDataResult.success && quizDataResult.data) {
        console.log('Quiz: Restoring previous answers from URL:', quizDataResult.data)
        setAnswers(quizDataResult.data.answers)
        setEmail(quizDataResult.data.email)
        
        // Set current question index based on answered questions
        const answeredQuestions = Object.keys(quizDataResult.data.answers).length
        if (answeredQuestions > 0 && answeredQuestions < questions.length) {
          setCurrentQuestionIndex(answeredQuestions)
        }
      }
    }
  }, [isDirectPayment, questions])

  // Auto-save answers to URL as user progresses through quiz
  useEffect(() => {
    if (Object.keys(answers).length > 0 && !isDirectPayment) {
      try {
        const encodedData = encodeQuizDataForURL(answers, email)
        if (encodedData) {
          const url = new URL(window.location.href)
          url.searchParams.set('qd', encodedData)
          window.history.replaceState({}, '', url.toString())
          console.log('Quiz: Auto-saved answers to URL')
        }
      } catch (error) {
        console.error('Quiz: Error auto-saving to URL:', error)
      }
    }
  }, [answers, email, isDirectPayment])

  useEffect(() => {
    if (isDirectPayment && questions.length > 0) {
      console.log('Quiz: Creating auto-answers for', questions.length, 'questions')
      const autoAnswers: Record<string, string> = {}
      questions.forEach(question => {
        autoAnswers[question.id] = question.options[0]
      })
      
      console.log('Quiz: Auto-answers created:', autoAnswers)
      console.log('Quiz: Auto-answers count:', Object.keys(autoAnswers).length)
      
      setTimeout(() => {
        if (isLoggedIn) {
          console.log('Quiz: Calling onComplete with auto-answers (logged in) - check payment status')
          onComplete(autoAnswers, undefined, { checkPaymentStatus: true })
        } else {
          console.log('Quiz: Calling onComplete with auto-answers (not logged in)')
          onComplete(autoAnswers, '<EMAIL>')
        }
      }, 1000)
    } else if (isDirectPayment) {
      console.log('Quiz: Direct payment mode but questions not loaded yet. Questions count:', questions.length)
    }
  }, [isDirectPayment, questions, onComplete, isLoggedIn])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>{isDirectPayment ? 'Auto-completing quiz...' : 'Loading quiz questions...'}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                Failed to load quiz questions: {error}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  // No questions available
  if (!questions.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert>
              <AlertDescription>
                No quiz questions are currently available.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  const currentQuestion = questions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100

  const handleAnswer = (answer: string) => {
    if (isTransitioning || selectedAnswer) return // Prevent multiple clicks

    // Step 1: Show selected answer immediately
    setSelectedAnswer(answer)

    // Step 2: Save the answer
    const newAnswers = { ...answers, [currentQuestion.id]: answer }
    setAnswers(newAnswers)

    // Step 3: Wait 300ms, then start horizontal transition
    setTimeout(() => {
      if (currentQuestionIndex < questions.length - 1) {
        // Start transition - current card slides out to right
        setTransitionDirection('out')
        setIsTransitioning(true)

        // After slide out animation, change question and prepare new card from right
        setTimeout(() => {
          setCurrentQuestionIndex(currentQuestionIndex + 1)
          setCardKey(prev => prev + 1)
          setSelectedAnswer(null) // Reset selection for next question
          setTransitionDirection('entering')
          
          // Start new card from right edge, then animate smoothly to center
          setTimeout(() => {
            setTransitionDirection('in')
            setIsTransitioning(false)
          }, 50)
        }, 200)
      } else {
        // On last question, show email input for non-logged-in users
        if (!isLoggedIn) {
          setShowEmailInput(true)
        } else {
          // Complete quiz for logged-in users - check payment status
          setTimeout(() => {
            onComplete(newAnswers, undefined, { checkPaymentStatus: true })
          }, 300)
        }
      }
    }, 750)
  }

  const handlePrevious = () => {
    if (isTransitioning || currentQuestionIndex === 0) return

    // Hide email input when going back from last question
    setShowEmailInput(false)
    
    setTransitionDirection('out-reverse')
    setIsTransitioning(true)

    setTimeout(() => {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
      setCardKey(prev => prev + 1)
      setSelectedAnswer(null) // Reset selection for previous question
      setTransitionDirection('entering-reverse')
      
      setTimeout(() => {
        setTransitionDirection('in')
        setIsTransitioning(false)
      }, 50)
    }, 200)
  }

  const handleSignIn = async () => {
    if (!email || !email.includes('@') || !password) return

    setIsSigningIn(true)
    setUserCreationError(null)

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
      })

      if (error) {
        setUserCreationError('Invalid email or password. Please check your credentials.')
        setIsSigningIn(false)
      } else if (data.user) {
        // Sign-in successful
        setSignInIndicator('✅ Signed in successfully!')
        setIsSigningIn(false)
        
        // Refresh auth context
        await refreshAuthState()
        
        console.log('Quiz: User signed in successfully, calling onComplete with delay')
        console.log('Quiz: Current answers:', answers)
        console.log('Quiz: Email:', email)
        
        // Small delay to let the success indicator show, then proceed
        setTimeout(() => {
          console.log('Quiz: Calling onComplete now')
          onComplete(answers, email)
        }, 500)
      }
    } catch (error) {
      console.error('Sign-in error:', error)
      setUserCreationError('An unexpected error occurred during sign-in.')
      setIsSigningIn(false)
    }
  }

  const handleCreateUserOrSignIn = async () => {
    if (!email || !email.includes('@')) return

    setIsCreatingUser(true)
    setUserCreationError(null)
    
    try {
      const result = await createUserAndSendReset(email)
      console.log('Create user or sign in result:', result)

      if (result.success && !result.userExists) {
        if (result.accessToken && result.refreshToken) {
          const { error: sessionError } = await supabase.auth.setSession({
            access_token: result.accessToken,
            refresh_token: result.refreshToken
          })
          await refreshAuthState()

          if (!sessionError) {
            setSignInIndicator('✅ Account created & signed in!')
            setTimeout(() => {
              // Follow the normal flow - go to Analysis first
              onComplete(answers, email)
            }, 1000)
            return
          }
        }
        setSignInIndicator('✅ Account created! Proceeding to analysis...')
        setTimeout(() => {
          // Follow the normal flow - go to Analysis first
          onComplete(answers, email)
        }, 1000)
      } else if (result.userExists) {
        setUserCreationError('User already exists. Please sign in.')
        setShowSignInOption(true)
        setIsCreatingUser(false)
      } else {
        setUserCreationError(result.message || 'Failed to create account')
        setIsCreatingUser(false)
      }
    } catch (error) {
      console.error('Error creating user:', error)
      setUserCreationError('An unexpected error occurred. Please try again.')
      setIsCreatingUser(false)
    }
  }

  return (
    <>
      {/* Fixed progress bar under sign in area */}
      <div className="fixed top-16 left-0 right-0 z-50">
        <div className="h-2 bg-emerald-100/40">
          <div
            className="h-2 bg-gradient-to-r from-emerald-500 via-teal-500 to-green-600 transition-all duration-700 ease-out rounded-r-full shadow-sm"
            style={{ width: `${progress}%` }}
          />
        </div>
        
        {/* Sign-in status indicator */}
        {signInIndicator && (
          <div className="bg-emerald-50 border-l-4 border-emerald-400 p-2 text-center">
            <div className="flex items-center justify-center space-x-2">
              <User className="w-4 h-4 text-emerald-600" />
              <span className="text-emerald-800 text-sm font-medium">{signInIndicator}</span>
            </div>
          </div>
        )}
      </div>

      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex items-center justify-center p-4 pt-20">
        {/* Return/Back Icon - Only show if not the first question */}
        {currentQuestionIndex > 0 && (
          <div className="fixed top-20 right-6 z-40">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevious}
              disabled={isTransitioning}
              className="flex items-center justify-center w-12 h-12 rounded-full bg-white/90 backdrop-blur-sm border border-emerald-200/50 text-emerald-700 hover:bg-emerald-50 hover:text-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <Undo2 className="w-6 h-6" />
            </Button>
          </div>
        )}
        
        <div className="w-full max-w-2xl overflow-hidden">
          <Card
            key={cardKey}
            className={`w-full transition-transform duration-200 ease-out bg-white/95 backdrop-blur-sm border-4 border-emerald-200/50`}
            style={{
              transform: isTransitioning && transitionDirection === 'out'
                ? 'translateX(-100%)'
                : transitionDirection === 'entering'
                ? 'translateX(100%)'
                : isTransitioning && transitionDirection === 'out-reverse'
                ? 'translateX(100%)'
                : transitionDirection === 'entering-reverse'
                ? 'translateX(-100%)'
                : 'translateX(0)'
            }}
          >
            <CardHeader>
              <CardTitle className="text-2xl font-semibold text-slate-800">
                {currentQuestion.text}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {currentQuestion.options.map((option, index) => {
                const isSelected = selectedAnswer === option
                const isDisabled = selectedAnswer !== null || isTransitioning

                return (
                  <Button
                    key={index}
                    variant={isSelected ? "default" : "outline"}
                    disabled={isDisabled}
                    className={`w-full justify-start text-left p-4 h-auto transition-all duration-300 ease-in-out
                      border-2 transform-gpu ${
                        isSelected
                          ? '!bg-emerald-600 !text-white !border-emerald-600 scale-[1.02]'
                          : isDisabled
                          ? 'cursor-not-allowed bg-gray-50 border-gray-200 text-gray-400 opacity-50'
                          : 'hover:bg-emerald-50 hover:border-emerald-300 hover:scale-[1.01] bg-white border-emerald-200/50 text-slate-700 hover:text-emerald-800'
                      }`}
                    onClick={() => handleAnswer(option)}
                  >
                    <span className="text-base leading-relaxed">{option}</span>
                  </Button>
                )
              })}

              {/* Email Input for Final Question */}
              {showEmailInput && (
                <div className="mt-6 p-6 bg-emerald-50 border border-emerald-200 rounded-lg">
                  {/* Account Creation Notice */}
                  <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-blue-800 mb-2">🎉 You're creating a LifeSupplier account!</h3>
                    <p className="text-sm text-blue-700 leading-relaxed mb-2">
                      By providing your email, you'll automatically get a free account with your personalized health report. 
                      This gives you access to:
                    </p>
                    <ul className="text-xs text-blue-700 space-y-1 ml-4">
                      <li>• Your complete health assessment results</li>
                      <li>• Unlimited future health quizzes</li>
                      <li>• Secure storage of your health data</li>
                      <li>• Progress tracking over time</li>
                    </ul>
                  </div>

                  <label htmlFor="email" className="block text-sm font-medium text-emerald-800 mb-2">
                    Enter your email to create account & get results
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="w-full px-3 py-2 border border-emerald-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 mb-4"
                    required
                  />
                  {email && !email.includes('@') && (
                    <p className="mt-1 mb-3 text-sm text-red-600">Please enter a valid email address</p>
                  )}
                  {userCreationError && (
                    <p className="mt-1 mb-3 text-sm text-red-600">{userCreationError}</p>
                  )}

                  {/* Consent Checkboxes */}
                  <div className="space-y-3 mb-4 p-4 bg-white border border-emerald-200 rounded-lg">
                    <h4 className="font-medium text-slate-800 text-sm mb-3">Required Agreements</h4>
                    
                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={agreedToTerms}
                        onChange={(e) => setAgreedToTerms(e.target.checked)}
                        className="mt-1 rounded border-emerald-300 focus:ring-emerald-500"
                        required
                      />
                      <span className="text-xs text-slate-700 leading-relaxed">
                        I agree to the{' '}
                        <span 
                          className="text-emerald-600 hover:text-emerald-700 underline cursor-pointer"
                          onClick={() => window.open('/terms', '_blank')}
                        >
                          Terms of Service
                        </span>
                        {' '}and understand this is for informational purposes only, not medical advice.
                      </span>
                    </label>

                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={agreedToPrivacy}
                        onChange={(e) => setAgreedToPrivacy(e.target.checked)}
                        className="mt-1 rounded border-emerald-300 focus:ring-emerald-500"
                        required
                      />
                      <span className="text-xs text-slate-700 leading-relaxed">
                        I agree to the{' '}
                        <span 
                          className="text-emerald-600 hover:text-emerald-700 underline cursor-pointer"
                          onClick={() => window.open('/privacy', '_blank')}
                        >
                          Privacy Policy
                        </span>
                        {' '}and consent to the collection and processing of my data.
                      </span>
                    </label>

                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={agreedToHealthData}
                        onChange={(e) => setAgreedToHealthData(e.target.checked)}
                        className="mt-1 rounded border-emerald-300 focus:ring-emerald-500"
                        required
                      />
                      <span className="text-xs text-slate-700 leading-relaxed">
                        I consent to processing my health assessment data to generate personalized recommendations 
                        and understand I can withdraw this consent anytime.
                      </span>
                    </label>

                    <div className="border-t border-slate-200 pt-3 mt-3">
                      <h4 className="font-medium text-slate-800 text-sm mb-2">Optional</h4>
                      <label className="flex items-start gap-3 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={optInMarketing}
                          onChange={(e) => setOptInMarketing(e.target.checked)}
                          className="mt-1 rounded border-emerald-300 focus:ring-emerald-500"
                        />
                        <span className="text-xs text-slate-600 leading-relaxed">
                          I'd like to receive helpful health tips and updates via email (you can unsubscribe anytime).
                        </span>
                      </label>
                    </div>
                  </div>

                  {/* Health Disclaimer */}
                  <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p className="text-xs text-orange-800 leading-relaxed">
                      <strong>Medical Disclaimer:</strong> Our recommendations are for educational purposes only and do not constitute medical advice. 
                      Always consult with a healthcare professional before starting any supplement regimen.
                    </p>
                  </div>
                  
                  {showSignInOption ? (
                    <div className="space-y-3">
                      <p className="text-emerald-700 text-sm">
                        An account with this email already exists. Please sign in:
                      </p>
                      
                      {/* Password Input */}
                      <div>
                        <label htmlFor="password" className="block text-sm font-medium text-emerald-800 mb-1">
                          Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPassword ? 'text' : 'password'}
                            id="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="Enter your password"
                            className="w-full px-3 py-2 pr-10 border border-emerald-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center text-emerald-500 hover:text-emerald-600"
                          >
                            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>

                      <Button
                        onClick={handleSignIn}
                        disabled={isSigningIn || !email.trim() || !password.trim()}
                        className="w-full bg-emerald-600 hover:bg-emerald-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSigningIn ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                            Signing In...
                          </>
                        ) : (
                          'Sign In to Get Results'
                        )}
                      </Button>
                      
                      <div className="text-center">
                        <button
                          type="button"
                          onClick={async () => {
                            // Send password reset email
                            try {
                              const { error } = await supabase.auth.resetPasswordForEmail(email, {
                                redirectTo: `${window.location.origin}/#reset-password`
                              })
                              
                              if (error) {
                                setUserCreationError('Failed to send password reset email.')
                              } else {
                                setUserCreationError(null)
                                alert('Password reset email sent! Check your inbox.')
                              }
                            } catch (error) {
                              setUserCreationError('Failed to send password reset email.')
                            }
                          }}
                          className="text-sm text-emerald-600 hover:text-emerald-700 underline"
                        >
                          Forgot your password?
                        </button>
                      </div>
                      
                      <Button
                        onClick={() => {
                          setShowSignInOption(false)
                          setPassword('')
                          setEmail('')
                        }}
                        variant="outline"
                        className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                      >
                        Use Different Email
                      </Button>
                    </div>
                  ) : (
                    <>
                      <Button
                        onClick={handleCreateUserOrSignIn}
                        disabled={!email || !email.includes('@') || !agreedToTerms || !agreedToPrivacy || !agreedToHealthData || isTransitioning || isCreatingUser}
                        className="w-full bg-emerald-600 hover:bg-emerald-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isCreatingUser ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                            Creating Your Account...
                          </>
                        ) : (
                          'Create Account & Get My Results'
                        )}
                      </Button>
                      
                      {/* Validation message for consent */}
                      {email && email.includes('@') && (!agreedToTerms || !agreedToPrivacy || !agreedToHealthData) && (
                        <p className="text-xs text-red-600 text-center mt-2">
                          Please agree to all required terms to continue
                        </p>
                      )}
                    </>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  )
}