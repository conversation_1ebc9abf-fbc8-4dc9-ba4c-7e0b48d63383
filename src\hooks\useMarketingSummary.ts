import { useState, useCallback } from 'react'
import { QuizResult, HealthTag } from './useQuizResultsAPI'

export interface MarketingSummary {
  healthAreas: {
    name: string
    description: string
    supplementCount: number
    foodCount: number
  }[]
  totalSupplements: number
  totalFoods: number
  totalHealthAreas: number
  previewMessage: string
  teaserSupplement?: string
}

export function useMarketingSummary() {
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const generateSummary = useCallback(async (
    results: QuizResult[], 
    healthTags: HealthTag[]
  ): Promise<MarketingSummary> => {
    try {
      setGenerating(true)
      setError(null)

      // Group results by health area
      const healthAreaMap = new Map<string, {
        name: string
        description: string
        supplements: Set<string>
        foods: Set<string>
      }>()

      // Initialize health areas from identified tags
      healthTags.forEach(tag => {
        healthAreaMap.set(tag.name, {
          name: tag.name,
          description: tag.description,
          supplements: new Set(),
          foods: new Set()
        })
      })

      // Count recommendations per health area
      results.forEach(result => {
        const healthArea = result.health_tag_name
        if (healthAreaMap.has(healthArea)) {
          const area = healthAreaMap.get(healthArea)!
          if (result.recommendation_type === 'supplement') {
            area.supplements.add(result.recommendation_id)
          } else {
            area.foods.add(result.recommendation_id)
          }
        }
      })

      // Convert to array format
      const healthAreas = Array.from(healthAreaMap.values()).map(area => ({
        name: area.name,
        description: area.description,
        supplementCount: area.supplements.size,
        foodCount: area.foods.size
      })).filter(area => area.supplementCount > 0 || area.foodCount > 0)

      // Calculate totals
      const totalSupplements = healthAreas.reduce((sum, area) => sum + area.supplementCount, 0)
      const totalFoods = healthAreas.reduce((sum, area) => sum + area.foodCount, 0)
      const totalHealthAreas = healthAreas.length

      // Generate preview message
      const previewMessage = generatePreviewMessage(healthAreas, totalSupplements, totalFoods)

      // Get a teaser supplement name (first supplement found)
      const teaserSupplement = results.find(r => r.recommendation_type === 'supplement')?.recommendation_name

      return {
        healthAreas,
        totalSupplements,
        totalFoods,
        totalHealthAreas,
        previewMessage,
        teaserSupplement
      }
    } catch (err) {
      console.error('Error generating marketing summary:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate summary'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setGenerating(false)
    }
  }, [])

  return { generateSummary, generating, error }
}

function generatePreviewMessage(
  healthAreas: { name: string; supplementCount: number; foodCount: number }[],
  totalSupplements: number,
  totalFoods: number
): string {
  if (healthAreas.length === 0) {
    return "Your personalized health recommendations are ready!"
  }

  // Find the health area with the most recommendations for teasing
  const topArea = healthAreas.reduce((max, area) => 
    (area.supplementCount + area.foodCount) > (max.supplementCount + max.foodCount) ? area : max
  )

  return `Found ${totalSupplements} supplements and ${totalFoods} foods for ${healthAreas.length} health areas • Including targeted support for ${topArea.name}`
}
