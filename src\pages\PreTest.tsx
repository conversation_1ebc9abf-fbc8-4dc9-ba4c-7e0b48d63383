import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface PreTestProps {
  onStartQuiz: () => void
}

type CardStep = 'hi' | 'before-start' | 'lets-start'

export function PreTest({ onStartQuiz }: PreTestProps) {
  const [currentStep, setCurrentStep] = useState<CardStep>('hi')
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [transitionDirection, setTransitionDirection] = useState<'out' | 'in' | 'entering'>('in')
  const [cardKey, setCardKey] = useState(0)

  useEffect(() => {
    if (currentStep === 'hi') {
      const timer = setTimeout(() => {
        // Transition to "before-start" card
        setIsTransitioning(true)
        setTransitionDirection('out')

        setTimeout(() => {
          setCurrentStep('before-start')
          setCardKey(prev => prev + 1)
          setTransitionDirection('entering')

          setTimeout(() => {
            setTransitionDirection('in')
            setIsTransitioning(false)
          }, 50)
        }, 300)
      }, 2000) // 2 seconds for hi card

      return () => clearTimeout(timer)
    } else if (currentStep === 'lets-start') {
      const timer = setTimeout(() => {
        // Start the quiz
        onStartQuiz()
      }, 2000) // 2 seconds for lets-start card

      return () => clearTimeout(timer)
    }
  }, [currentStep, onStartQuiz])

  const handleContinue = () => {
    // Transition to "lets-start" card
    setIsTransitioning(true)
    setTransitionDirection('out')

    setTimeout(() => {
      setCurrentStep('lets-start')
      setCardKey(prev => prev + 1)
      setTransitionDirection('entering')

      setTimeout(() => {
        setTransitionDirection('in')
        setIsTransitioning(false)
      }, 50)
    }, 300)
  }

  const renderHiCard = () => (
    <div className="text-center">
      <h1 className="text-4xl font-bold text-emerald-600">Hi!</h1>
    </div>
  )

  const renderBeforeStartCard = () => (
    <div className="text-center space-y-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">
        Before you start...
      </h1>

      {/* Privacy statements */}
      <div className="space-y-4 text-left max-w-md mx-auto mb-8">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <p className="text-lg text-gray-900 leading-relaxed">
            Your answers are anonymized, and we do not sell your data.
          </p>
        </div>

        <div className="flex items-start gap-3">
          <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <p className="text-lg text-gray-900 leading-relaxed">
            Answer each question honestly for most accurate results.
          </p>
        </div>
      </div>

      {/* Continue button */}
      <div className="text-center">
        <Button
          onClick={handleContinue}
          className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
        >
          Continue
        </Button>
      </div>
    </div>
  )

  const renderLetsStartCard = () => (
    <div className="text-center">
      <h1 className="text-4xl font-bold text-emerald-600">Let's start!</h1>
    </div>
  )

  const getCurrentCard = () => {
    switch (currentStep) {
      case 'hi':
        return renderHiCard()
      case 'before-start':
        return renderBeforeStartCard()
      case 'lets-start':
        return renderLetsStartCard()
      default:
        return renderHiCard()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex items-center justify-center p-4">

      <div className="w-full max-w-lg overflow-hidden">
        {currentStep === 'before-start' ? (
          <Card
            key={cardKey}
            className={`w-full transition-transform duration-300 ease-out bg-white/95 backdrop-blur-sm border-4 border-emerald-200/50 p-8`}
            style={{
              transform: isTransitioning && transitionDirection === 'out'
                ? 'translateX(-100%)'
                : transitionDirection === 'entering'
                ? 'translateX(100%)'
                : 'translateX(0)'
            }}
          >
            <div className="flex items-center justify-center min-h-[300px]">
              {getCurrentCard()}
            </div>
          </Card>
        ) : (
          <div
            key={cardKey}
            className={`w-full transition-transform duration-300 ease-out`}
            style={{
              transform: isTransitioning && transitionDirection === 'out'
                ? 'translateX(-100%)'
                : transitionDirection === 'entering'
                ? 'translateX(100%)'
                : 'translateX(0)'
            }}
          >
            <div className="flex items-center justify-center min-h-[200px]">
              {getCurrentCard()}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}