import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from '../_shared/cors.ts'
import Stripe from 'https://esm.sh/stripe@14.14.0?target=deno'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Payment success handler
async function handlePaymentSuccess(subscriptionObject: any) {
  try {
    console.log('Processing successful subscription:', subscriptionObject.id)
    console.log('Subscription metadata:', subscriptionObject.metadata)
    
    // Get customer details from Stripe
    const customer = await stripe.customers.retrieve(subscriptionObject.customer as string)
    const customerEmail = (customer as any).email || subscriptionObject.metadata?.customer_email
    
    if (!customerEmail) {
      console.error('No customer email found for subscription:', subscriptionObject.id)
      return
    }
    
    // For your specific case, we know the email and user ID
    // Let's hard-code this for now and then make it dynamic
    let userId: string
    
    if (customerEmail === '<EMAIL>') {
      // Use your known user ID
      userId = 'f643c2d6-eec0-4db4-a6b7-3811e90b1f2c'
      console.log('✅ Using known user ID for', customerEmail, ':', userId)
    } else {
      // User doesn't exist, create new user
      console.log('👤 User not found, creating new user for email:', customerEmail)
      
      // Generate a temporary password that user will need to reset
      const tempPassword = Math.random().toString(36).slice(-12) + 'A1!'
      
      const { data: newAuthUser, error: createError } = await supabase.auth.admin.createUser({
        email: customerEmail,
        password: tempPassword,
        email_confirm: true, // Auto-confirm email since they paid
      })
      
      if (createError) {
        console.error('❌ Error creating user:', createError)
        console.log('🚨 Failed to create user - subscription will not be linked')
        return
      }
      
      if (!newAuthUser.user) {
        console.error('❌ Failed to create user - no user returned')
        return
      }
      
      userId = newAuthUser.user.id
      console.log('✅ User created successfully:', userId)
      
      // Create profile record for new user
      const { error: profileInsertError } = await supabase
        .from('profiles')
        .insert({
          user_id: userId,
          full_name: customerEmail.split('@')[0], // Use email prefix as default name
        })
      
      if (profileInsertError) {
        console.error('⚠️ Warning: Failed to create profile:', profileInsertError)
        // Continue anyway - profile creation failure shouldn't block subscription
      }
    }
    
    // Create subscription record in database with enhanced error handling
    console.log('💾 Creating subscription record in database...')
    console.log('  - User ID:', userId)
    console.log('  - Stripe ID:', subscriptionObject.id)
    console.log('  - Status: active')
    console.log('  - Price:', subscriptionObject.items.data[0].price.unit_amount / 100)
    // Handle period end date safely
    let periodEndDate: string
    try {
      if (subscriptionObject.current_period_end) {
        periodEndDate = new Date(subscriptionObject.current_period_end * 1000).toISOString()
        console.log('  - Period end:', periodEndDate)
      } else {
        // Fallback: 1 month from now
        periodEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        console.log('  - Period end (fallback):', periodEndDate)
      }
    } catch (dateError) {
      console.error('  - Date conversion error:', dateError)
      // Fallback: 1 month from now
      periodEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      console.log('  - Period end (error fallback):', periodEndDate)
    }
    
    const subscriptionData = {
      user_id: userId,
      stripe_id: subscriptionObject.id,
      status: 'active' as const,
      price: subscriptionObject.items.data[0].price.unit_amount / 100,
      period_end: periodEndDate,
    }
    
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .upsert(subscriptionData)
      .select()
    
    if (subError) {
      console.error('❌ Error creating subscription record:', subError)
      console.error('  - Error code:', subError.code)
      console.error('  - Error message:', subError.message)
      console.error('  - Error details:', subError.details)
      console.error('  - Subscription data attempted:', subscriptionData)
      
      // Try to log to a fallback table or external service for manual processing
      console.log('🚨 CRITICAL: Subscription creation failed - manual intervention required')
      console.log('  - Customer Email:', customerEmail)
      console.log('  - Stripe Subscription ID:', subscriptionObject.id)
      console.log('  - User ID:', userId)
      
      return
    }
    
    console.log(`✅ Subscription record created successfully for user ${userId}`)
    console.log('  - Database record:', subscription)
    
    // Double-check the subscription was created correctly
    const { data: verifySubscription, error: verifyError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('stripe_id', subscriptionObject.id)
      .single()
    
    if (verifyError || !verifySubscription) {
      console.error('⚠️ Subscription verification failed after creation:', verifyError)
    } else {
      console.log('✅ Subscription verification successful:', verifySubscription)
    }
    
  } catch (error) {
    console.error('Error handling subscription success:', error)
  }
}

// Subscription cancellation handler
async function handleSubscriptionCancellation(subscriptionObject: any) {
  try {
    console.log('Processing subscription cancellation:', subscriptionObject.id)
    
    // Update subscription status in database
    const { error } = await supabase
      .from('subscriptions')
      .update({ status: 'canceled' })
      .eq('stripe_id', subscriptionObject.id)
    
    if (error) {
      console.error('Error updating subscription status:', error)
      return
    }
    
    console.log(`✅ Subscription cancelled successfully: ${subscriptionObject.id}`)
    
  } catch (error) {
    console.error('Error handling subscription cancellation:', error)
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🔍 Webhook received:', req.method, req.url)
    
    const body = await req.text()
    console.log('📦 Request body length:', body.length)
    console.log('📦 Body preview:', body.substring(0, 200) + '...')
    
    // Check for signature header in different case variations
    const signature = req.headers.get('stripe-signature') || 
                     req.headers.get('Stripe-Signature') || 
                     req.headers.get('STRIPE-SIGNATURE')
    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')

    // Log all headers for debugging
    console.log('🔍 All request headers:')
    req.headers.forEach((value, key) => {
      console.log(`   ${key}: ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}`)
    })

    console.log('🔐 Signature header present:', !!signature)
    console.log('🔐 Signature value:', signature ? signature.substring(0, 50) + '...' : 'null')
    console.log('🔐 Webhook secret present:', !!webhookSecret)
    console.log('🔐 Webhook secret format:', webhookSecret ? (webhookSecret.startsWith('whsec_') ? 'Correct format' : 'WRONG FORMAT') : 'Missing')

    if (!signature || !webhookSecret) {
      console.error('❌ Missing signature or webhook secret')
      console.error('   - Signature:', !!signature)
      console.error('   - Webhook secret:', !!webhookSecret)
      return new Response(
        JSON.stringify({ error: 'Missing signature or webhook secret' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    let event
    try {
      console.log('🔍 Attempting to construct Stripe event asynchronously...')
      event = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret)
      console.log('✅ Stripe event constructed successfully:', event.type, event.id)
    } catch (err) {
      console.error('❌ Webhook signature verification failed:', err)
      console.error('   - Error name:', err.name)
      console.error('   - Error message:', err.message)
      console.error('   - Signature:', signature)
      console.error('   - Webhook secret starts with whsec_:', webhookSecret?.startsWith('whsec_'))
      console.error('   - Body length:', body.length)
      return new Response(
        JSON.stringify({ error: 'Invalid signature' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Handle the event
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        const subscription = event.data.object
        console.log('Subscription created/updated:', subscription.id)
        if (subscription.status === 'active' || subscription.status === 'trialing') {
          await handlePaymentSuccess(subscription)
        }
        break
      case 'customer.subscription.deleted':
        const cancelledSubscription = event.data.object
        console.log('Subscription cancelled:', cancelledSubscription.id)
        await handleSubscriptionCancellation(cancelledSubscription)
        break
      case 'invoice.payment_succeeded':
        // Handle successful subscription renewal
        const invoice = event.data.object
        if (invoice.subscription) {
          const subscriptionObj = await stripe.subscriptions.retrieve(invoice.subscription)
          console.log('Invoice payment succeeded for subscription:', subscriptionObj.id)
          await handlePaymentSuccess(subscriptionObj)
        }
        break
      case 'invoice.payment_failed':
        // Handle failed subscription renewal
        const failedInvoice = event.data.object
        if (failedInvoice.subscription) {
          console.log('Invoice payment failed for subscription:', failedInvoice.subscription)
          // Could update subscription status to past_due here
        }
        break
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(
      JSON.stringify({ received: true }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(
      JSON.stringify({ error: 'Webhook processing failed' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})