# QuizHub Backend Server

A minimal Deno backend for processing quiz results and handling payments.

## Setup

1. **Install Deno** (if not already installed):
   ```bash
   curl -fsSL https://deno.land/install.sh | sh
   ```

2. **Environment Variables**:
   Copy `.env.example` to `.env` and fill in your values:
   ```bash
   cp .env.example .env
   ```

3. **Development**:
   ```bash
   deno task dev
   ```

4. **Production**:
   ```bash
   deno task start
   ```

## API Endpoints

### Quiz Processing
- `POST /api/quiz/process` - Process quiz answers and return recommendations
  ```json
  {
    "answers": {
      "1": "Male",
      "2": "30-40",
      "3": "Low",
      ...
    }
  }
  ```

### Payments (Stripe)
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/webhooks/stripe` - Handle Stripe webhooks

### Health Check
- `GET /health` - Server health status

## Architecture

- **Stateless**: No database required for quiz processing
- **Secure**: All sensitive operations happen server-side
- **Minimal**: Only essential endpoints for the frontend

## Development

The server runs on `http://localhost:8000` and is proxied by Vite during frontend development.