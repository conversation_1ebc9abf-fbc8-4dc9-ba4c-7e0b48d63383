-- ============================================================================
-- Core Schema Setup - Run this first
-- ============================================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing types if they exist to avoid conflicts
DROP TYPE IF EXISTS question_type CASCADE;
DROP TYPE IF EXISTS recommendation_type CASCADE;
DROP TYPE IF EXISTS subscription_status CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;

-- Create custom types
CREATE TYPE question_type AS ENUM ('multiple-choice', 'yes-no', 'scale');
CREATE TYPE recommendation_type AS ENUM ('supplement', 'food');
CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'past_due');
CREATE TYPE user_role AS ENUM ('user', 'admin');

-- ============================================================================
-- Table Creation (in dependency order)
-- ============================================================================

-- 1. Profiles table - extends auth.users with additional info
CREATE TABLE IF NOT EXISTS profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    role user_role DEFAULT 'user',
    newsletter_opt_in BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Questions table - stores all quiz questions (no dependencies)
CREATE TABLE IF NOT EXISTS questions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    text TEXT NOT NULL,
    type question_type NOT NULL,
    options TEXT[] NOT NULL,
    "order" INTEGER NOT NULL,
    parent_id UUID REFERENCES questions(id),
    condition TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Health tags table - maps answers to health issues (no dependencies)
CREATE TABLE IF NOT EXISTS health_tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tag_name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    triggers JSONB NOT NULL, -- Array of {question_id, answer} objects
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Recommendations table - depends on health_tags
CREATE TABLE IF NOT EXISTS recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    health_tag_id UUID REFERENCES health_tags(id) ON DELETE CASCADE,
    type recommendation_type NOT NULL,
    name TEXT NOT NULL,
    details JSONB NOT NULL, -- {dosage, timing, benefits, description}
    affiliate_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Responses table - depends on auth.users and questions
CREATE TABLE IF NOT EXISTS responses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
    answer JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Reports table - depends on auth.users
CREATE TABLE IF NOT EXISTS reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    quiz_responses JSONB NOT NULL,
    health_tags TEXT[],
    recommendations JSONB NOT NULL,
    pdf_url TEXT,
    payment_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Subscriptions table - depends on auth.users
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_id TEXT NOT NULL UNIQUE,
    status subscription_status NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Indexes for Performance
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_questions_order ON questions("order");
CREATE INDEX IF NOT EXISTS idx_questions_parent_id ON questions(parent_id);
CREATE INDEX IF NOT EXISTS idx_responses_user_id ON responses(user_id);
CREATE INDEX IF NOT EXISTS idx_responses_question_id ON responses(question_id);
CREATE INDEX IF NOT EXISTS idx_reports_user_id ON reports(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_id ON subscriptions(stripe_id);
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_health_tag_id ON recommendations(health_tag_id);