import { HEALTH_CONDITIONS, SUPPLEMENTS, FOOD_RECOMMENDATIONS } from "./data.ts";

export interface QuizResult {
  health_tag_name: string
  health_tag_description: string
  recommendation_type: 'supplement' | 'food'
  recommendation_id: string
  recommendation_name: string
  recommendation_details: {
    description: string
    benefits: string[]
    side_effects?: string[]
    contraindications?: string[]
    dosage_info?: {
      min_dose?: string
      max_dose?: string
      timing?: string
      form?: string
      with_food?: boolean
    }
    interactions?: string[]
    pregnancy_safe?: boolean
    breastfeeding_safe?: boolean
    rationale?: string
    all_conditions?: string[]
    condition_count?: number
    nutritional_info?: any
    serving_suggestions?: string[]
  }
  priority_score?: number
  condition_count?: number
  all_conditions?: string[]
  priority?: number
  score?: number
}

// Comprehensive quiz processing using CSV data
export function processQuizAnswers(answers: Record<string, string>): QuizResult[] {
  const results: QuizResult[] = []
  const identifiedConditions = new Set<string>()

  // Analyze answers to identify health conditions
  analyzeEnergyLevels(answers, identifiedConditions)
  analyzeSleepQuality(answers, identifiedConditions)
  analyzeJointHealth(answers, identifiedConditions)
  analyzePhysicalActivity(answers, identifiedConditions)
  analyzeStressLevels(answers, identifiedConditions)
  analyzeDigestiveHealth(answers, identifiedConditions)
  analyzeImmuneHealth(answers, identifiedConditions)
  analyzeHairHealth(answers, identifiedConditions)
  analyzeBreathingHealth(answers, identifiedConditions)

  // Generate recommendations for each identified condition
  let priorityScore = 100
  identifiedConditions.forEach(conditionName => {
    const condition = HEALTH_CONDITIONS.find(c => c.name === conditionName)
    if (!condition) return

    // Add supplement recommendations
    const supplements = SUPPLEMENTS.filter(s => s.condition_name === conditionName)
    supplements.forEach(supplement => {
      results.push(createSupplementResult(supplement, condition, priorityScore))
      priorityScore -= 5
    })

    // Add food recommendations
    const foods = FOOD_RECOMMENDATIONS.filter(f => f.condition_name === conditionName)
    foods.forEach(food => {
      results.push(createFoodResult(food, condition, priorityScore))
      priorityScore -= 3
    })
  })

  // If no conditions identified, add general wellness
  if (results.length === 0) {
    results.push(createGeneralWellnessResult())
  }

  return results.sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0))
}

function analyzeEnergyLevels(answers: Record<string, string>, conditions: Set<string>) {
  const energyLevel = answers['550e8400-e29b-41d4-a716-446655440003']
  const canFocus = answers['33ddc48a-3741-428b-b877-173b0168ebf9']
  const sugarCravings = answers['452ac791-288b-48aa-98ab-80d2173b2240']
  
  if (energyLevel === 'Very low' || energyLevel === 'Low' || 
      canFocus === 'No' || sugarCravings === 'Yes') {
    conditions.add('Low Energy')
  }
}

function analyzeSleepQuality(answers: Record<string, string>, conditions: Set<string>) {
  const troubleSleeping = answers['550e8400-e29b-41d4-a716-446655440004']
  const wakeUpRefreshed = answers['598022d6-cece-4d65-a457-dcfe80a3a1fb']
  
  if (troubleSleeping === 'Yes' || wakeUpRefreshed === 'No') {
    conditions.add('Sleep Quality Issues')
  }
}

function analyzeJointHealth(answers: Record<string, string>, conditions: Set<string>) {
  const jointPain = answers['550e8400-e29b-41d4-a716-446655440005']
  const jointsFlexible = answers['b941ea42-0943-49e1-95a3-462f3debcc03']
  
  if (jointPain === 'Yes' || jointsFlexible === 'No') {
    conditions.add('Joint Support Needed')
  }
}

function analyzePhysicalActivity(answers: Record<string, string>, conditions: Set<string>) {
  const exerciseFreq = answers['550e8400-e29b-41d4-a716-446655440006']
  const feelStrong = answers['ce586653-1155-4563-839f-266623795bae']
  
  if (exerciseFreq === 'Never' || exerciseFreq === '1-2 times per week' || 
      feelStrong === 'No') {
    conditions.add('Physical Activity Support')
  }
}

function analyzeStressLevels(answers: Record<string, string>, conditions: Set<string>) {
  const regularStress = answers['550e8400-e29b-41d4-a716-446655440008']
  const workLifeBalance = answers['5b3879a5-e825-4fff-b786-b7bc1b4cc025']
  
  if (regularStress === 'Yes' || workLifeBalance === 'No') {
    conditions.add('Stress Management')
  }
}

function analyzeDigestiveHealth(answers: Record<string, string>, conditions: Set<string>) {
  const digestiveHealth = answers['550e8400-e29b-41d4-a716-446655440009']
  
  if (digestiveHealth === 'Poor' || digestiveHealth === 'Fair') {
    conditions.add('Digestive Health Support')
  }
}

function analyzeImmuneHealth(answers: Record<string, string>, conditions: Set<string>) {
  const getsSick = answers['2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc']
  const eatsFruitsVeggies = answers['b2254912-7bb7-4428-a365-b7c0de7c8bf5']
  
  if (getsSick === 'No' || eatsFruitsVeggies === 'No') {
    conditions.add('Immune System Support')
  }
}

function analyzeHairHealth(answers: Record<string, string>, conditions: Set<string>) {
  const hairHealthy = answers['aeda2a7e-b897-4bc0-a67f-20f1306c83d0']
  
  if (hairHealthy === 'No') {
    conditions.add('Hair & Nail Health')
  }
}

function analyzeBreathingHealth(answers: Record<string, string>, conditions: Set<string>) {
  const breatheComfortably = answers['e2715890-51c7-4582-a89e-5007c3efb634']
  
  if (breatheComfortably === 'No') {
    conditions.add('Breathing Health')
  }
}

function createSupplementResult(supplement: any, condition: any, priority: number): QuizResult {
  return {
    health_tag_name: condition.name,
    health_tag_description: condition.description,
    recommendation_type: 'supplement',
    recommendation_id: supplement.name.toLowerCase().replace(/\s+/g, '-'),
    recommendation_name: supplement.name,
    recommendation_details: {
      description: supplement.description,
      benefits: supplement.benefits,
      side_effects: supplement.side_effects,
      contraindications: supplement.contraindications,
      dosage_info: {
        min_dose: supplement.min_dose,
        max_dose: supplement.max_dose,
        timing: supplement.timing,
        form: supplement.form,
        with_food: supplement.with_food
      },
      interactions: supplement.interactions,
      pregnancy_safe: supplement.pregnancy_safe,
      breastfeeding_safe: supplement.breastfeeding_safe
    },
    priority_score: priority,
    condition_count: 1,
    all_conditions: [condition.name]
  }
}

function createFoodResult(food: any, condition: any, priority: number): QuizResult {
  return {
    health_tag_name: condition.name,
    health_tag_description: condition.description,
    recommendation_type: 'food',
    recommendation_id: food.name.toLowerCase().replace(/\s+/g, '-'),
    recommendation_name: food.name,
    recommendation_details: {
      description: food.description,
      benefits: food.benefits,
      serving_suggestions: food.serving_suggestions
    },
    priority_score: priority,
    condition_count: 1,
    all_conditions: [condition.name]
  }
}

function createGeneralWellnessResult(): QuizResult {
  return {
    health_tag_name: 'General Wellness',
    health_tag_description: 'Foundation support for overall health',
    recommendation_type: 'supplement',
    recommendation_id: 'multivitamin',
    recommendation_name: 'High-Quality Multivitamin',
    recommendation_details: {
      description: 'A comprehensive multivitamin to fill nutritional gaps and support overall health.',
      benefits: [
        'Fills nutritional gaps',
        'Supports immune function',
        'Promotes energy metabolism',
        'Maintains overall health'
      ],
      dosage_info: {
        min_dose: 'As directed on label',
        timing: 'With breakfast',
        form: 'Capsule or tablet',
        with_food: true
      }
    },
    priority_score: 50,
    condition_count: 1,
    all_conditions: ['General Wellness']
  }
}