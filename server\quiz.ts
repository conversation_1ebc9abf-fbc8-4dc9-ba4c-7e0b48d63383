import { HEALTH_CONDITIONS, SUPPLEMENTS, FOOD_RECOMMENDATIONS } from "./data.ts";

export interface QuizResult {
  health_tag_name: string
  health_tag_description: string
  recommendation_type: 'supplement' | 'food'
  recommendation_id: string
  recommendation_name: string
  recommendation_details: {
    description: string
    benefits: string[]
    side_effects?: string[]
    contraindications?: string[]
    dosage_info?: {
      min_dose?: string
      max_dose?: string
      timing?: string
      form?: string
      with_food?: boolean
    }
    interactions?: string[]
    pregnancy_safe?: boolean
    breastfeeding_safe?: boolean
    rationale?: string
    all_conditions?: string[]
    condition_count?: number
    nutritional_info?: any
    serving_suggestions?: string[]
  }
  priority_score?: number
  condition_count?: number
  all_conditions?: string[]
  priority?: number
  score?: number
}

// Comprehensive quiz processing using CSV data
export function processQuizAnswers(answers: Record<string, string>): QuizResult[] {
  const results: QuizResult[] = []
  const identifiedConditions = new Set<string>()

  // Analyze answers to identify health conditions
  analyzeEnergyLevels(answers, identifiedConditions)
  analyzeSleepQuality(answers, identifiedConditions)
  analyzeJointHealth(answers, identifiedConditions)
  analyzePhysicalActivity(answers, identifiedConditions)
  analyzeStressLevels(answers, identifiedConditions)
  analyzeDigestiveHealth(answers, identifiedConditions)
  analyzeImmuneHealth(answers, identifiedConditions)
  analyzeHairHealth(answers, identifiedConditions)
  analyzeBreathingHealth(answers, identifiedConditions)

  // Generate recommendations for each identified condition
  let priorityScore = 100
  identifiedConditions.forEach(conditionName => {
    const condition = HEALTH_CONDITIONS.find(c => c.name === conditionName)
    if (!condition) return

    // Add supplement recommendations
    const supplements = SUPPLEMENTS.filter(s => s.condition_name === conditionName)
    supplements.forEach(supplement => {
      results.push(createSupplementResult(supplement, condition, priorityScore))
      priorityScore -= 5
    })

    // Add food recommendations
    const foods = FOOD_RECOMMENDATIONS.filter(f => f.condition_name === conditionName)
    foods.forEach(food => {
      results.push(createFoodResult(food, condition, priorityScore))
      priorityScore -= 3
    })
  })

  // If no conditions identified, add general wellness
  if (results.length === 0) {
    results.push(createGeneralWellnessResult())
  }

  return results.sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0))
}

function analyzeEnergyLevels(answers: Record<string, string>, conditions: Set<string>) {
  const avoidAfternoonCrashes = answers['550e8400-e29b-41d4-a716-**********03']
  const canFocus = answers['33ddc48a-3741-428b-b877-173b0168ebf9']
  const stableWithoutSugarCravings = answers['452ac791-288b-48aa-98ab-80d2173b2240']
  
  // If they can't avoid crashes, can't focus, or have sugar cravings - they have low energy
  if (avoidAfternoonCrashes === 'No' || canFocus === 'No' || stableWithoutSugarCravings === 'No') {
    conditions.add('Low Energy')
  }
}

function analyzeSleepQuality(answers: Record<string, string>, conditions: Set<string>) {
  const sleepWell = answers['550e8400-e29b-41d4-a716-**********04'] // "Do you sleep well without trouble?"
  const wakeUpRefreshed = answers['598022d6-cece-4d65-a457-dcfe80a3a1fb'] // "I wake up feeling rested and refreshed"
  
  // If they don't sleep well or don't wake up refreshed - they have sleep issues
  if (sleepWell === 'No' || wakeUpRefreshed === 'No') {
    conditions.add('Sleep Quality Issues')
  }
}

function analyzeJointHealth(answers: Record<string, string>, conditions: Set<string>) {
  const jointsFreeFromPain = answers['550e8400-e29b-41d4-a716-**********05'] // "Are your joints free from pain and stiffness?"
  const jointsFlexible = answers['b941ea42-0943-49e1-95a3-462f3debcc03'] // "My joints and hips feel flexible and comfortable"
  
  // If joints are NOT free from pain or NOT flexible - they need joint support
  if (jointsFreeFromPain === 'No' || jointsFlexible === 'No') {
    conditions.add('Joint Support Needed')
  }
}

function analyzePhysicalActivity(answers: Record<string, string>, conditions: Set<string>) {
  const canClimbStairs = answers['550e8400-e29b-41d4-a716-**********06'] // "Can you climb stairs or do light activities without getting winded?"
  const feelStrong = answers['ce586653-1155-4563-839f-266623795bae'] // "I feel physically strong and have good stamina"
  
  // If they can't climb stairs or don't feel strong - they need physical activity support
  if (canClimbStairs === 'No' || feelStrong === 'No') {
    conditions.add('Physical Activity Support')
  }
}

function analyzeStressLevels(answers: Record<string, string>, conditions: Set<string>) {
  const feelCalmAndStressFree = answers['550e8400-e29b-41d4-a716-**********08'] // "Do you feel calm and stress-free most of the time?"
  const stayCalmWithoutOverwhelm = answers['5b3879a5-e825-4fff-b786-b7bc1b4cc025'] // "Do you stay calm without feeling overwhelmed or anxious most days?"
  
  // If they don't feel calm or do feel overwhelmed - they need stress management
  if (feelCalmAndStressFree === 'No' || stayCalmWithoutOverwhelm === 'No') {
    conditions.add('Stress Management')
  }
}

function analyzeDigestiveHealth(answers: Record<string, string>, conditions: Set<string>) {
  const comfortableDigestion = answers['550e8400-e29b-41d4-a716-**********09'] // "Do you have comfortable digestion without bloating, stomach pain, or irregular bowel movements?"
  
  // If they don't have comfortable digestion - they need digestive support
  if (comfortableDigestion === 'No') {
    conditions.add('Digestive Health Support')
  }
}

function analyzeImmuneHealth(answers: Record<string, string>, conditions: Set<string>) {
  const dontGetSickEasily = answers['2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc'] // "I don't get easily sick or get the flu"
  const maintainStableEnergy = answers['b2254912-7bb7-4428-a365-b7c0de7c8bf5'] // "Do you maintain stable energy without frequent food cravings or weakness between meals?"
  
  // If they DO get sick easily or DON'T maintain stable energy - they need immune support  
  if (dontGetSickEasily === 'No' || maintainStableEnergy === 'No') {
    conditions.add('Immune System Support')
  }
}

function analyzeHairHealth(answers: Record<string, string>, conditions: Set<string>) {
  const hairHealthy = answers['aeda2a7e-b897-4bc0-a67f-20f1306c83d0'] // "My hair is healthy and not thinning"
  
  // If hair is NOT healthy - they need hair support
  if (hairHealthy === 'No') {
    conditions.add('Hair & Nail Health')
  }
}

function analyzeBreathingHealth(answers: Record<string, string>, conditions: Set<string>) {
  const breatheComfortably = answers['e2715890-51c7-4582-a89e-5007c3efb634'] // "I breathe comfortably and deeply without difficulty"
  
  // If they DON'T breathe comfortably - they need breathing support
  if (breatheComfortably === 'No') {
    conditions.add('Breathing Health')
  }
}

function createSupplementResult(supplement: any, condition: any, priority: number): QuizResult {
  return {
    health_tag_name: condition.name,
    health_tag_description: condition.description,
    recommendation_type: 'supplement',
    recommendation_id: supplement.name.toLowerCase().replace(/\s+/g, '-'),
    recommendation_name: supplement.name,
    recommendation_details: {
      description: supplement.description,
      benefits: supplement.benefits,
      side_effects: supplement.side_effects,
      contraindications: supplement.contraindications,
      dosage_info: {
        min_dose: supplement.min_dose,
        max_dose: supplement.max_dose,
        timing: supplement.timing,
        form: supplement.form,
        with_food: supplement.with_food
      },
      interactions: supplement.interactions,
      pregnancy_safe: supplement.pregnancy_safe,
      breastfeeding_safe: supplement.breastfeeding_safe
    },
    priority_score: priority,
    condition_count: 1,
    all_conditions: [condition.name]
  }
}

function createFoodResult(food: any, condition: any, priority: number): QuizResult {
  return {
    health_tag_name: condition.name,
    health_tag_description: condition.description,
    recommendation_type: 'food',
    recommendation_id: food.name.toLowerCase().replace(/\s+/g, '-'),
    recommendation_name: food.name,
    recommendation_details: {
      description: food.description,
      benefits: food.benefits,
      serving_suggestions: food.serving_suggestions
    },
    priority_score: priority,
    condition_count: 1,
    all_conditions: [condition.name]
  }
}

function createGeneralWellnessResult(): QuizResult {
  return {
    health_tag_name: 'General Wellness',
    health_tag_description: 'Foundation support for overall health',
    recommendation_type: 'supplement',
    recommendation_id: 'multivitamin',
    recommendation_name: 'High-Quality Multivitamin',
    recommendation_details: {
      description: 'A comprehensive multivitamin to fill nutritional gaps and support overall health.',
      benefits: [
        'Fills nutritional gaps',
        'Supports immune function',
        'Promotes energy metabolism',
        'Maintains overall health'
      ],
      dosage_info: {
        min_dose: 'As directed on label',
        timing: 'With breakfast',
        form: 'Capsule or tablet',
        with_food: true
      }
    },
    priority_score: 50,
    condition_count: 1,
    all_conditions: ['General Wellness']
  }
}