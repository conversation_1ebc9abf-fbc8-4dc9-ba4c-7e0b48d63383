-- ============================================================================
-- Database Functions and Triggers - Run this third
-- ============================================================================

-- Drop existing functions/triggers if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS on_profiles_updated ON public.profiles;
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.handle_updated_at();

-- ============================================================================
-- Function to automatically create profile on user signup
-- ============================================================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (user_id, full_name)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
EXCEPTION
    WHEN others THEN
        -- Log error but don't fail user creation
        RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- Function to update updated_at timestamp
-- ============================================================================

CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Triggers
-- ============================================================================

-- Trigger to create profile on user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Trigger to update updated_at on profiles
CREATE OR REPLACE TRIGGER on_profiles_updated
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- ============================================================================
-- Helper Functions for Quiz Logic
-- ============================================================================

-- Function to get questions in order
CREATE OR REPLACE FUNCTION get_quiz_questions()
RETURNS TABLE (
    id UUID,
    text TEXT,
    type question_type,
    options TEXT[],
    question_order INTEGER,
    parent_id UUID,
    condition TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        q.id,
        q.text,
        q.type,
        q.options,
        q."order" as question_order,
        q.parent_id,
        q.condition
    FROM questions q
    ORDER BY q."order" ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process quiz answers and get recommendations
CREATE OR REPLACE FUNCTION process_quiz_results(user_answers JSONB)
RETURNS TABLE (
    health_tag_name TEXT,
    health_tag_description TEXT,
    recommendation_id UUID,
    recommendation_type recommendation_type,
    recommendation_name TEXT,
    recommendation_details JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ht.tag_name as health_tag_name,
        ht.description as health_tag_description,
        r.id as recommendation_id,
        r.type as recommendation_type,
        r.name as recommendation_name,
        r.details as recommendation_details
    FROM health_tags ht
    JOIN recommendations r ON r.health_tag_id = ht.id
    WHERE EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(ht.triggers) as trigger_obj
        WHERE user_answers ? (trigger_obj->>'question_id')
        AND user_answers->(trigger_obj->>'question_id') ? (trigger_obj->>'answer')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;