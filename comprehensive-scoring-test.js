// Comprehensive Test: Enhanced Scoring System Across Different Health Tag Scenarios
// Testing 1, 2, 3, 4+ health tag combinations

// Complete supplement database with realistic condition mappings
const SUPPLEMENTS = [
  // Multi-condition supplements (cover 2+ conditions)
  { name: "Ceylon Cinnamon", condition_names: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"] },
  { name: "Chromium Picolinate", condition_names: ["Low Energy & Blood Sugar", "Weight Management Support"] },
  { name: "B-Complex Vitamins", condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus"] },
  { name: "Omega-3 Fish Oil", condition_names: ["Joint Support Needed", "Brain Fog & Focus"] },
  { name: "Vitamin D3", condition_names: ["Immune Support", "Stress & Lifestyle Balance"] },
  { name: "Magnesium Glycinate", condition_names: ["Sleep Quality Issues", "Physical Performance"] },
  { name: "Ashwagandha", condition_names: ["Stress & Lifestyle Balance", "Physical Performance"] },
  { name: "Green Tea Extract", condition_names: ["Weight Management Support", "Cardiovascular Health Support"] },
  { name: "Hawthorn Berry", condition_names: ["Cardiovascular Health Support", "Digestive Health Support"] },
  { name: "Zinc Picolinate", condition_names: ["Hair & Skin Support", "Immune Support"] },
  
  // Single-condition supplements
  { name: "L-Theanine", condition_names: ["Caffeine Dependency"] },
  { name: "Coenzyme Q10", condition_names: ["Sedentary Lifestyle"] },
  { name: "Digestive Enzymes", condition_names: ["Digestive Health Support"] },
  { name: "Quercetin", condition_names: ["Respiratory & Allergy Support"] },
  { name: "Rhodiola Rosea", condition_names: ["Physical Performance"] },
  { name: "Biotin", condition_names: ["Hair & Skin Support"] }
];

// Enhanced scoring function (matches updated-edge-function.js)
function calculateEnhancedScore(supplement, userConditions) {
  const coverageConditions = supplement.condition_names.filter(cond => userConditions.includes(cond));
  
  if (coverageConditions.length === 0) return 0;
  
  let score = 100; // Base score for covering at least 1 condition
  
  // Add +120 bonus for each additional condition beyond the first
  if (coverageConditions.length > 1) {
    score += (coverageConditions.length - 1) * 120;
  }
  
  return score;
}

// Weighted random selection
function weightedRandomSelection(supplements, scores) {
  const totalWeight = scores.reduce((sum, score) => sum + score, 0);
  if (totalWeight === 0) return null;
  
  let random = Math.random() * totalWeight;
  
  for (let i = 0; i < supplements.length; i++) {
    random -= scores[i];
    if (random <= 0) return supplements[i];
  }
  return supplements[supplements.length - 1];
}

// Test scenarios with different health tag combinations
const TEST_SCENARIOS = [
  {
    name: "SCENARIO 1: Single Health Tag",
    description: "User with only low energy issues",
    conditions: ["Low Energy & Blood Sugar"],
    userProfile: "Emma, 28, Office Worker - Always tired after lunch"
  },
  {
    name: "SCENARIO 2: Two Health Tags", 
    description: "User with blood sugar + cardiovascular issues",
    conditions: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"],
    userProfile: "Robert, 45, Manager - Energy crashes + heart concerns"
  },
  {
    name: "SCENARIO 3: Three Health Tags",
    description: "User with energy, focus, and stress issues", 
    conditions: ["Low Energy & Blood Sugar", "Brain Fog & Focus", "Stress & Lifestyle Balance"],
    userProfile: "Sarah, 35, Working Mom - Tired, unfocused, stressed"
  },
  {
    name: "SCENARIO 4: Four Health Tags",
    description: "User with multiple interconnected health issues",
    conditions: ["Low Energy & Blood Sugar", "Sleep Quality Issues", "Stress & Lifestyle Balance", "Physical Performance"],
    userProfile: "Mike, 38, Entrepreneur - Poor sleep, stress, low energy, wants to exercise"
  },
  {
    name: "SCENARIO 5: Five Health Tags",
    description: "User with comprehensive health optimization needs",
    conditions: ["Low Energy & Blood Sugar", "Brain Fog & Focus", "Immune Support", "Joint Support Needed", "Digestive Health Support"],
    userProfile: "Lisa, 42, Health-conscious - Multiple wellness goals"
  }
];

console.log("🧪 COMPREHENSIVE ENHANCED SCORING SYSTEM TEST");
console.log("Testing 1-5 Health Tag Scenarios");
console.log("=".repeat(90));

TEST_SCENARIOS.forEach((scenario, scenarioIndex) => {
  console.log(`\n${scenario.name}`);
  console.log(`👤 ${scenario.userProfile}`);
  console.log(`🎯 Conditions: ${scenario.conditions.join(", ")}`);
  console.log("-".repeat(90));
  
  // Find applicable supplements and calculate scores
  const applicableSupplements = [];
  const scores = [];
  
  SUPPLEMENTS.forEach(supplement => {
    const score = calculateEnhancedScore(supplement, scenario.conditions);
    if (score > 0) {
      applicableSupplements.push(supplement);
      scores.push(score);
    }
  });
  
  // Sort by score for display
  const supplementsWithScores = applicableSupplements.map((supp, i) => ({
    supplement: supp,
    score: scores[i],
    coverageConditions: supp.condition_names.filter(cond => scenario.conditions.includes(cond)),
    coverageCount: supp.condition_names.filter(cond => scenario.conditions.includes(cond)).length
  })).sort((a, b) => b.score - a.score);
  
  // Calculate probabilities
  const totalWeight = scores.reduce((sum, score) => sum + score, 0);
  
  console.log(`📊 Available Supplements (${applicableSupplements.length} total):`);
  console.log("Supplement".padEnd(20) + " | Score | Prob  | Covers");
  console.log("-".repeat(70));
  
  // Show top 8 supplements to keep output manageable
  supplementsWithScores.slice(0, 8).forEach(item => {
    const probability = (item.score / totalWeight * 100).toFixed(1);
    const typeIcon = item.coverageCount > 1 ? "⭐" : "🔸";
    const coverageText = `${item.coverageCount} cond${item.coverageCount > 1 ? 's' : ''}`;
    
    console.log(
      `${typeIcon} ${item.supplement.name.padEnd(17)} | ${item.score.toString().padEnd(5)} | ${probability.padEnd(5)}% | ${coverageText}`
    );
  });
  
  if (supplementsWithScores.length > 8) {
    console.log(`   ... and ${supplementsWithScores.length - 8} more supplements`);
  }
  
  // Simulation to show actual selection distribution
  console.log(`\n🎲 Selection Simulation (1,000 picks):`);
  const simulationResults = {};
  const simulations = 1000;
  
  for (let i = 0; i < simulations; i++) {
    const selected = weightedRandomSelection(applicableSupplements, scores);
    if (selected) {
      simulationResults[selected.name] = (simulationResults[selected.name] || 0) + 1;
    }
  }
  
  // Show top 5 most selected supplements
  const topSelections = Object.entries(simulationResults)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5);
    
  topSelections.forEach(([name, count], index) => {
    const percent = (count / simulations * 100).toFixed(1);
    const medal = index === 0 ? "🥇" : index === 1 ? "🥈" : index === 2 ? "🥉" : `${index + 1}.`;
    const supplement = supplementsWithScores.find(s => s.supplement.name === name);
    const typeIcon = supplement && supplement.coverageCount > 1 ? "⭐" : "🔸";
    
    console.log(`   ${medal} ${name} - ${percent}% ${typeIcon}`);
  });
  
  // Key insights for this scenario
  const multiConditionSupps = supplementsWithScores.filter(s => s.coverageCount > 1);
  const singleConditionSupps = supplementsWithScores.filter(s => s.coverageCount === 1);
  
  console.log(`\n💡 Key Insights:`);
  console.log(`   • Multi-condition supplements: ${multiConditionSupps.length}`);
  console.log(`   • Single-condition supplements: ${singleConditionSupps.length}`);
  
  if (multiConditionSupps.length > 0) {
    const topMulti = multiConditionSupps[0];
    const topMultiProb = (topMulti.score / totalWeight * 100).toFixed(1);
    console.log(`   • Best multi-condition: ${topMulti.supplement.name} (${topMultiProb}% chance)`);
    console.log(`   • Covers: ${topMulti.coverageConditions.join(" + ")}`);
  }
  
  if (scenarioIndex < TEST_SCENARIOS.length - 1) {
    console.log("\n" + "=".repeat(90));
  }
});

console.log("\n" + "=".repeat(90));
console.log("🎯 SYSTEM PERFORMANCE SUMMARY");
console.log("=".repeat(90));

console.log(`\n📈 Enhanced Scoring Benefits Across Scenarios:`);
console.log(`   ✅ Single Health Tag: Simple, focused recommendations`);
console.log(`   ✅ Two Health Tags: Multi-condition supplements get 120+ point bonus`);
console.log(`   ✅ Three+ Health Tags: Complex coverage optimization`);
console.log(`   ✅ Consistent Logic: More coverage = Higher priority`);

console.log(`\n🎯 Scoring Formula Recap:`);
console.log(`   • Base Score: 100 points (covers 1+ conditions)`);
console.log(`   • Multi-Condition Bonus: +120 points per additional condition`);
console.log(`   • Example: 2 conditions = 100 + (1 × 120) = 220 points`);
console.log(`   • Example: 3 conditions = 100 + (2 × 120) = 340 points`);

console.log(`\n🚀 Result: Users get optimal supplements for their specific health profile!`);
