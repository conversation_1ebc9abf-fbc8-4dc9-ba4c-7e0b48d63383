{"name": "lifesupplier", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:supabase": "supabase start", "dev:functions": "supabase functions serve", "dev:full": "concurrently \"npm run dev:functions\" \"npm run dev\"", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:quiz": "node --import tsx/esm scripts/test-quiz-scenarios.ts"}, "dependencies": {"@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@supabase/supabase-js": "^2.46.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@eslint/js": "^9.13.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.3", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "concurrently": "^9.2.0", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "jsdom": "^26.1.0", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^6.0.1", "vitest": "^3.2.4"}}