-- ============================================================================
-- COMPREHENSIVE SUPPLEMENT DATABASE UPDATE
-- Run this in Supabase SQL Editor to update the database with enhanced supplement data
-- ============================================================================

-- First, create the supplement_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'supplement_type') THEN
        CREATE TYPE supplement_type AS ENUM ('supplement', 'vitamin', 'mineral', 'protein', 'herb', 'other');
    END IF;
END $$;

-- Drop existing recommendations table to recreate with new structure
DROP TABLE IF EXISTS recommendations CASCADE;

-- Create enhanced supplements table
CREATE TABLE IF NOT EXISTS supplements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    type supplement_type DEFAULT 'supplement',
    description TEXT,
    benefits TEXT[],
    side_effects TEXT[],
    contraindications TEXT[],
    dosage_info JSONB,
    interactions TEXT[],
    pregnancy_safe BOOLEAN DEFAULT false,
    breastfeeding_safe BOOLEAN DEFAULT false,
    age_restrictions JSONB,
    scoring_criteria JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create supplement recommendations junction table
CREATE TABLE IF NOT EXISTS supplement_recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    health_tag_id UUID REFERENCES health_tags(id) ON DELETE CASCADE,
    supplement_id UUID REFERENCES supplements(id) ON DELETE CASCADE,
    priority INTEGER DEFAULT 1,
    score INTEGER DEFAULT 1,
    rationale TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(health_tag_id, supplement_id)
);

-- Create food recommendations table
CREATE TABLE IF NOT EXISTS food_recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    health_tag_id UUID REFERENCES health_tags(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    benefits TEXT[],
    nutritional_info JSONB,
    serving_suggestions TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_supplements_name ON supplements(name);
CREATE INDEX IF NOT EXISTS idx_supplement_recommendations_health_tag ON supplement_recommendations(health_tag_id);
CREATE INDEX IF NOT EXISTS idx_supplement_recommendations_supplement ON supplement_recommendations(supplement_id);
CREATE INDEX IF NOT EXISTS idx_food_recommendations_health_tag ON food_recommendations(health_tag_id);

-- Enable RLS
ALTER TABLE supplements ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplement_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE food_recommendations ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Supplements are publicly readable" ON supplements;
DROP POLICY IF EXISTS "Supplement recommendations are publicly readable" ON supplement_recommendations;
DROP POLICY IF EXISTS "Food recommendations are publicly readable" ON food_recommendations;
DROP POLICY IF EXISTS "Admins can manage supplements" ON supplements;
DROP POLICY IF EXISTS "Admins can manage supplement recommendations" ON supplement_recommendations;
DROP POLICY IF EXISTS "Admins can manage food recommendations" ON food_recommendations;

-- Create RLS policies
CREATE POLICY "Supplements are publicly readable" ON supplements FOR SELECT USING (true);
CREATE POLICY "Supplement recommendations are publicly readable" ON supplement_recommendations FOR SELECT USING (true);
CREATE POLICY "Food recommendations are publicly readable" ON food_recommendations FOR SELECT USING (true);

CREATE POLICY "Admins can manage supplements" ON supplements 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage supplement recommendations" ON supplement_recommendations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage food recommendations" ON food_recommendations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Clear existing data
DELETE FROM supplements;
DELETE FROM supplement_recommendations;
DELETE FROM food_recommendations;

-- Insert comprehensive supplement data
INSERT INTO supplements (name, type, description, benefits, side_effects, contraindications, dosage_info, pregnancy_safe, breastfeeding_safe) VALUES

('Multivitamin', 'vitamin',
'Comprehensive nutritional foundation providing essential vitamins and minerals',
ARRAY['Fills nutritional gaps', 'Supports overall health', 'Immune function', 'Energy metabolism', 'Supports stress response', 'Brain and cognitive support'],
ARRAY['May cause stomach upset if taken on empty stomach', 'Iron may cause constipation', 'B vitamins may turn urine bright yellow'],
ARRAY['Avoid if taking specific medications without consulting doctor', 'Some ingredients may interact with blood thinners'],
'{"min_dose": "1 tablet daily", "max_dose": "1 tablet daily", "timing": "With breakfast", "form": "tablet/capsule", "with_food": true}'::jsonb,
true, true),

('Vitamin D3', 'vitamin',
'Essential for bone health, immune function, and mood regulation',
ARRAY['Bone health', 'Immune support', 'Mood regulation', 'Muscle function', 'Supports calcium absorption'],
ARRAY['High doses may cause nausea', 'Can increase calcium levels excessively', 'May cause kidney stones with very high doses'],
ARRAY['Avoid high doses if have kidney disease', 'Monitor levels if taking digoxin'],
'{"min_dose": "1000 IU daily", "max_dose": "4000 IU daily", "timing": "With a meal containing fat", "form": "soft gel", "with_food": true}'::jsonb,
true, true),

('Vitamin C', 'vitamin',
'Powerful antioxidant supporting immune function and collagen synthesis',
ARRAY['Antioxidant protection', 'Immune support', 'Collagen synthesis', 'Iron absorption', 'Wound healing'],
ARRAY['High doses may cause digestive upset', 'May cause diarrhea above 2000mg', 'Can increase iron absorption potentially causing overload'],
ARRAY['Reduce dose if prone to kidney stones', 'May interact with certain chemotherapy drugs'],
'{"min_dose": "500mg daily", "max_dose": "2000mg daily", "timing": "With meals", "form": "tablet/powder", "with_food": true}'::jsonb,
true, true),

('Vitamin B12', 'vitamin',
'Essential for nerve function, red blood cell formation, and DNA synthesis',
ARRAY['Energy production', 'Nerve function', 'Red blood cell formation', 'Brain health', 'Mood support'],
ARRAY['Generally very safe', 'Rare allergic reactions possible', 'May interact with certain medications'],
ARRAY['Monitor if taking metformin', 'May mask folate deficiency'],
'{"min_dose": "250mcg daily", "max_dose": "1000mcg daily", "timing": "Morning", "form": "sublingual/tablet", "with_food": false}'::jsonb,
true, true),

('Magnesium Glycinate', 'mineral',
'Highly absorbable form of magnesium for muscle, nerve, and sleep support',
ARRAY['Muscle relaxation', 'Nerve function', 'Sleep quality', 'Stress relief', 'Heart health', 'Bone health'],
ARRAY['May cause loose stools in high doses', 'Can cause drowsiness', 'May lower blood pressure'],
ARRAY['Reduce dose if have kidney disease', 'May interact with certain antibiotics'],
'{"min_dose": "200mg daily", "max_dose": "400mg daily", "timing": "Evening", "form": "capsule", "with_food": true}'::jsonb,
true, true),

('Zinc', 'mineral',
'Essential mineral for immune function, wound healing, and protein synthesis',
ARRAY['Immune support', 'Wound healing', 'Taste and smell', 'Protein synthesis', 'Skin health'],
ARRAY['May cause nausea on empty stomach', 'Can cause metallic taste', 'High doses may reduce copper absorption'],
ARRAY['Avoid high doses long-term', 'May interact with certain antibiotics'],
'{"min_dose": "8mg daily", "max_dose": "15mg daily", "timing": "With food", "form": "tablet/lozenge", "with_food": true}'::jsonb,
true, true),

('Iron Bisglycinate', 'mineral',
'Highly absorbable form of iron for energy support',
ARRAY['Prevents anemia', 'Energy production', 'Oxygen transport', 'Cognitive function', 'Immune support'],
ARRAY['Constipation', 'Stomach upset', 'Nausea', 'Dark stools', 'Metallic taste'],
ARRAY['Avoid if have hemochromatosis', 'Monitor levels regularly', 'May interact with certain medications'],
'{"min_dose": "18mg daily", "max_dose": "27mg daily", "timing": "Between meals with vitamin C", "form": "tablet", "with_food": false}'::jsonb,
true, true),

('Omega-3 Fish Oil', 'supplement',
'Essential fatty acids for heart, brain, and joint health',
ARRAY['Heart health', 'Brain function', 'Joint health', 'Anti-inflammatory', 'Eye health'],
ARRAY['Fishy aftertaste', 'May cause bloating', 'Can increase bleeding risk', 'Digestive upset'],
ARRAY['Monitor if taking blood thinners', 'Avoid before surgery'],
'{"min_dose": "1000mg daily", "max_dose": "3000mg daily", "timing": "With meals", "form": "soft gel", "with_food": true}'::jsonb,
false, false),

('Turmeric Curcumin', 'herb',
'Potent anti-inflammatory compound from turmeric root',
ARRAY['Anti-inflammatory', 'Antioxidant support', 'Joint health', 'Brain health', 'Immune support'],
ARRAY['May cause stomach upset', 'Can increase bleeding risk', 'May cause heartburn'],
ARRAY['Avoid if have gallstones', 'Monitor if taking blood thinners'],
'{"min_dose": "500mg daily", "max_dose": "1000mg daily", "timing": "With meals and black pepper", "form": "capsule", "with_food": true}'::jsonb,
false, false),

('Creatine Monohydrate', 'supplement',
'Supports muscle energy, strength, and power output',
ARRAY['Increased muscle strength', 'Power output', 'Muscle recovery', 'Brain health', 'Exercise performance'],
ARRAY['May cause water retention', 'Initial weight gain from water', 'Rare digestive upset'],
ARRAY['Increase water intake', 'Monitor if have kidney concerns'],
'{"min_dose": "3g daily", "max_dose": "5g daily", "timing": "Post-workout or anytime", "form": "powder", "with_food": false}'::jsonb,
false, false),

('Ashwagandha', 'herb',
'Adaptogenic herb for stress management and cortisol balance',
ARRAY['Reduces cortisol levels', 'Improves stress response', 'Supports mood', 'Enhances energy', 'Sleep quality'],
ARRAY['May cause drowsiness', 'Stomach upset in some people', 'May lower blood pressure'],
ARRAY['Avoid if pregnant or breastfeeding', 'May interact with sedatives'],
'{"min_dose": "300mg daily", "max_dose": "600mg daily", "timing": "Morning or evening with food", "form": "capsule", "with_food": true}'::jsonb,
false, false),

('L-Theanine', 'supplement',
'Amino acid that promotes calm alertness without drowsiness',
ARRAY['Calm alertness', 'Stress reduction', 'Focus improvement', 'Sleep quality', 'Anxiety relief'],
ARRAY['Very few side effects', 'Rare headaches', 'Potential dizziness'],
ARRAY['Generally very safe', 'Monitor if taking sedatives'],
'{"min_dose": "100mg daily", "max_dose": "200mg daily", "timing": "Morning or as needed", "form": "capsule", "with_food": false}'::jsonb,
false, false),

('Probiotics', 'supplement',
'Beneficial bacteria for digestive and immune health',
ARRAY['Digestive health', 'Immune support', 'Gut microbiome balance', 'Nutrient absorption'],
ARRAY['Initial bloating possible', 'Gas during adjustment period', 'Rare infections in immunocompromised'],
ARRAY['Avoid if severely immunocompromised', 'Monitor with certain medical conditions'],
'{"min_dose": "10 billion CFU daily", "max_dose": "50 billion CFU daily", "timing": "With or after meals", "form": "capsule", "with_food": true}'::jsonb,
true, true),

('Digestive Enzymes', 'supplement',
'Enzymes that help break down proteins, fats, and carbohydrates',
ARRAY['Improves nutrient breakdown', 'Reduces bloating', 'Better digestion', 'Nutrient absorption'],
ARRAY['Rare allergic reactions', 'May cause nausea', 'Stomach upset if taken without food'],
ARRAY['Avoid if have active stomach ulcers', 'Monitor if taking medications'],
'{"min_dose": "1 capsule with meals", "max_dose": "2 capsules with meals", "timing": "Beginning of each meal", "form": "capsule", "with_food": true}'::jsonb,
true, true),

('Melatonin', 'supplement',
'Natural hormone that regulates sleep-wake cycles',
ARRAY['Sleep onset', 'Sleep quality', 'Jet lag recovery', 'Circadian rhythm regulation'],
ARRAY['Morning grogginess', 'Vivid dreams', 'Potential next-day drowsiness'],
ARRAY['Avoid if have autoimmune conditions', 'May interact with blood thinners'],
'{"min_dose": "0.5mg", "max_dose": "3mg", "timing": "30 minutes before bed", "form": "tablet", "with_food": false}'::jsonb,
false, false);

-- Link supplements to health conditions
INSERT INTO supplement_recommendations (health_tag_id, supplement_id, priority, score, rationale) VALUES

-- Low Energy recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'), 
 (SELECT id FROM supplements WHERE name = 'Multivitamin'), 1, 1,
 'Diets may lack essential nutrients; multivitamins provide B vitamins, vitamin D, and iron to fill nutritional gaps'),

((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin B12'), 1, 1,
 'B12 deficiency causes fatigue and weakness due to poor oxygen delivery'),

((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'), 
 (SELECT id FROM supplements WHERE name = 'Iron Bisglycinate'), 1, 1,
 'Iron is essential for hemoglobin production and oxygen transport; deficiency causes fatigue'),

-- Sleep Quality Issues recommendations  
((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'), 
 (SELECT id FROM supplements WHERE name = 'Magnesium Glycinate'), 1, 1,
 'Supports relaxation, sleep onset, and quality through nervous system regulation'),

((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'), 
 (SELECT id FROM supplements WHERE name = 'Melatonin'), 1, 1,
 'Natural hormone that regulates circadian rhythm and improves sleep onset'),

-- Joint Support recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'), 
 (SELECT id FROM supplements WHERE name = 'Omega-3 Fish Oil'), 1, 1,
 'Anti-inflammatory properties help reduce joint discomfort and support mobility'),

((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'), 
 (SELECT id FROM supplements WHERE name = 'Turmeric Curcumin'), 1, 1,
 'Potent anti-inflammatory compound that helps reduce joint pain and inflammation'),

-- Physical Activity Support recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin D3'), 1, 1,
 'Essential for bone health, muscle function, and immune system'),

((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'), 
 (SELECT id FROM supplements WHERE name = 'Creatine Monohydrate'), 1, 1,
 'Supports short bursts of energy, strength, and muscle recovery'),

-- Stress Management recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Ashwagandha'), 1, 1,
 'Adaptogenic herb that reduces cortisol levels and improves stress response'),

((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'L-Theanine'), 1, 1,
 'Promotes calm alertness and reduces stress without causing drowsiness'),

((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Magnesium Glycinate'), 2, 1,
 'Stress depletes magnesium; helps calm the nervous system and supports relaxation'),

-- Digestive Health Support recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'), 
 (SELECT id FROM supplements WHERE name = 'Probiotics'), 1, 1,
 'Beneficial bacteria that support gut health, improve digestion, and boost immune function'),

((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'), 
 (SELECT id FROM supplements WHERE name = 'Digestive Enzymes'), 1, 1,
 'Enzymes that help break down proteins, fats, and carbohydrates for better digestion'),

-- Additional multi-condition supplement relationships based on CSV analysis

-- Vitamin B12 for multiple conditions (from CSV: energy, brain fog, concentration, mood)
((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin B12'), 2, 1,
 'B12 deficiency causes fatigue and weakness due to poor oxygen delivery'),

((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin B12'), 3, 1,
 'B12 plays a role in neurotransmitter synthesis and mood regulation'),

-- Zinc for multiple conditions (stress, mood, skin health)
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Zinc'), 3, 1,
 'Zinc supports immune and antioxidant defenses affected by stress'),

-- Vitamin D3 for additional conditions (mood, immune support)
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin D3'), 3, 1,
 'Vitamin D supports mood regulation and helps with seasonal mood changes'),

((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin D3'), 3, 1,
 'Low vitamin D is linked with poor sleep quality and circadian rhythm disruption'),

-- Omega-3 for additional conditions (brain health, mood)
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Omega-3 Fish Oil'), 2, 1,
 'Omega-3 fatty acids support brain health and may help with stress response'),

-- Multivitamin for additional targeted conditions
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Multivitamin'), 3, 1,
 'B-complex vitamins and magnesium in multivitamins support nervous system under stress'),

((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'), 
 (SELECT id FROM supplements WHERE name = 'Multivitamin'), 2, 1,
 'Athletes have higher nutrient turnover; B vitamins, magnesium, and antioxidants support energy metabolism'),

-- Vitamin C for stress and immune support
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin C'), 3, 1,
 'Stress increases oxidative damage; vitamin C is a potent antioxidant that helps reduce stress-induced inflammation');

-- Update food recommendations
INSERT INTO food_recommendations (health_tag_id, name, description, benefits, serving_suggestions) VALUES

-- Low Energy foods
((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'),
 'Iron-Rich Foods', 
 'Foods high in iron like spinach, lean red meat, lentils, and quinoa',
 ARRAY['Prevents anemia', 'Supports oxygen transport', 'Boosts energy', 'Improves concentration'],
 ARRAY['Include vitamin C foods to enhance absorption', 'Combine with citrus fruits', 'Cook in cast iron when possible']),

((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'),
 'Complex Carbohydrates', 
 'Whole grains, sweet potatoes, oats, and brown rice for steady energy',
 ARRAY['Sustained energy release', 'Stable blood sugar', 'B-vitamin rich', 'Fiber for gut health'],
 ARRAY['Choose whole grain versions', 'Pair with protein', 'Eat regularly throughout day']),

-- Sleep support foods
((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'),
 'Tart Cherry Juice', 
 'Natural source of melatonin for better sleep',
 ARRAY['Natural melatonin', 'Improves sleep duration', 'Reduces inflammation', 'Antioxidant properties'],
 ARRAY['8oz 1-2 hours before bed', 'Choose unsweetened varieties', 'Can be diluted with water']),

((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'),
 'Magnesium-Rich Foods', 
 'Almonds, spinach, pumpkin seeds, and dark chocolate',
 ARRAY['Promotes relaxation', 'Better sleep quality', 'Muscle function', 'Nervous system support'],
 ARRAY['Snack on almonds 1 hour before bed', 'Add pumpkin seeds to evening meals', 'Choose 70%+ dark chocolate']),

-- Joint support foods
((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'),
 'Fatty Fish & Walnuts', 
 'Salmon, sardines, mackerel, and walnuts for omega-3 fatty acids',
 ARRAY['Anti-inflammatory omega-3s', 'Joint support', 'Heart healthy', 'Brain food'],
 ARRAY['Aim for 2-3 servings per week', 'Choose wild-caught when possible', 'Add walnuts to salads and yogurt']),

((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'),
 'Colorful Berries', 
 'Blueberries, cherries, and strawberries rich in anthocyanins',
 ARRAY['Antioxidant compounds', 'Anti-inflammatory', 'Vitamin C', 'Fiber'],
 ARRAY['1 cup daily fresh or frozen', 'Mix into smoothies', 'Add to breakfast cereals']),

-- Physical activity foods
((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'),
 'Protein-Rich Foods', 
 'Lean meats, eggs, Greek yogurt, and legumes for muscle support',
 ARRAY['Muscle building', 'Recovery support', 'Satiety', 'Metabolic boost'],
 ARRAY['Include protein with each meal', 'Post-workout within 30 minutes', 'Vary protein sources']),

((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'),
 'Bananas & Sweet Potatoes', 
 'Natural sources of potassium and complex carbs',
 ARRAY['Natural electrolytes', 'Potassium for muscles', 'Energy for workouts', 'Post-exercise recovery'],
 ARRAY['Eat banana 30 minutes before workout', 'Sweet potato post-workout', 'Great for potassium replacement']),

-- Stress management foods
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'),
 'Green Tea & Dark Chocolate', 
 'Natural stress-reducing compounds and healthy treats',
 ARRAY['L-theanine for calm', 'Antioxidants', 'Mood support', 'Cognitive benefits'],
 ARRAY['2-3 cups green tea daily', '1 oz dark chocolate (70%+)', 'Avoid late in day for sleep']),

((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'),
 'Adaptogenic Foods', 
 'Mushrooms, ginseng tea, and maca powder',
 ARRAY['Stress response support', 'Energy balance', 'Immune support', 'Mental clarity'],
 ARRAY['Add medicinal mushrooms to soups', 'Try ginseng tea in afternoon', 'Blend maca into smoothies']),

-- Digestive health foods
((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'),
 'Fermented Foods', 
 'Yogurt, kefir, sauerkraut, kimchi, and kombucha',
 ARRAY['Natural probiotics', 'Digestive enzymes', 'Immune support', 'Nutrient density'],
 ARRAY['Include one serving daily', 'Start small and increase gradually', 'Choose unpasteurized when possible']),

((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'),
 'Fiber-Rich Foods', 
 'Vegetables, fruits, legumes, and whole grains',
 ARRAY['Gut microbiome support', 'Regular digestion', 'Nutrient absorption', 'Satiety'],
 ARRAY['Increase gradually to avoid bloating', 'Drink plenty of water', 'Aim for 25-35g daily']),

-- General wellness foods (for all tags)
((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'),
 'Leafy Green Vegetables', 
 'Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods',
 ARRAY['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
 ARRAY['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']),

((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'),
 'Leafy Green Vegetables', 
 'Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods',
 ARRAY['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
 ARRAY['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']),

((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'),
 'Leafy Green Vegetables', 
 'Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods',
 ARRAY['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
 ARRAY['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']),

((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'),
 'Leafy Green Vegetables', 
 'Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods',
 ARRAY['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
 ARRAY['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']),

((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'),
 'Leafy Green Vegetables', 
 'Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods',
 ARRAY['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
 ARRAY['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']),

((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'),
 'Leafy Green Vegetables', 
 'Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods',
 ARRAY['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
 ARRAY['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']);

-- Enhanced process_quiz_results function with multi-condition supplement aggregation
CREATE OR REPLACE FUNCTION process_quiz_results(user_answers JSONB)
RETURNS TABLE (
    health_tag_name TEXT,
    health_tag_description TEXT,
    recommendation_type TEXT,
    recommendation_id UUID,
    recommendation_name TEXT,
    recommendation_details JSONB,
    priority INTEGER,
    score INTEGER,
    condition_count INTEGER,
    all_conditions TEXT[]
) AS $$
BEGIN
    -- Return aggregated supplement recommendations (grouped by supplement with multiple conditions)
    RETURN QUERY
    WITH matched_conditions AS (
        SELECT ht.id as health_tag_id, ht.tag_name, ht.description
        FROM health_tags ht
        WHERE EXISTS (
            SELECT 1 
            FROM jsonb_array_elements(ht.triggers) as trigger_obj
            WHERE user_answers ? (trigger_obj->>'question_id')
            AND user_answers->(trigger_obj->>'question_id') ? (trigger_obj->>'answer')
        )
    ),
    supplement_aggregates AS (
        SELECT 
            s.id as supplement_id,
            s.name as supplement_name,
            s.description,
            s.benefits,
            s.side_effects,
            s.contraindications,
            s.dosage_info,
            s.interactions,
            s.pregnancy_safe,
            s.breastfeeding_safe,
            MIN(sr.priority) as min_priority,
            MAX(sr.score) as max_score,
            COUNT(DISTINCT mc.health_tag_id) as condition_count,
            ARRAY_AGG(DISTINCT mc.tag_name ORDER BY sr.priority) as all_conditions,
            ARRAY_AGG(DISTINCT mc.description ORDER BY sr.priority) as all_descriptions,
            STRING_AGG(DISTINCT sr.rationale, ' • ' ORDER BY sr.priority) as combined_rationale
        FROM matched_conditions mc
        JOIN supplement_recommendations sr ON sr.health_tag_id = mc.health_tag_id
        JOIN supplements s ON s.id = sr.supplement_id
        GROUP BY s.id, s.name, s.description, s.benefits, s.side_effects, s.contraindications, 
                 s.dosage_info, s.interactions, s.pregnancy_safe, s.breastfeeding_safe
    )
    SELECT 
        sa.all_conditions[1] as health_tag_name,
        sa.all_descriptions[1] as health_tag_description,
        'supplement'::TEXT as recommendation_type,
        sa.supplement_id as recommendation_id,
        sa.supplement_name as recommendation_name,
        jsonb_build_object(
            'description', sa.description,
            'benefits', sa.benefits,
            'side_effects', sa.side_effects,
            'contraindications', sa.contraindications,
            'dosage_info', sa.dosage_info,
            'interactions', sa.interactions,
            'pregnancy_safe', sa.pregnancy_safe,
            'breastfeeding_safe', sa.breastfeeding_safe,
            'rationale', sa.combined_rationale,
            'all_conditions', sa.all_conditions,
            'condition_count', sa.condition_count
        ) as recommendation_details,
        -- Multi-condition supplements get boosted priority
        CASE 
            WHEN sa.condition_count >= 3 THEN 0  -- Highest priority for 3+ conditions
            WHEN sa.condition_count = 2 THEN sa.min_priority - 1  -- Boost by 1 for 2 conditions
            ELSE sa.min_priority
        END as priority,
        -- Multi-condition supplements get bonus score
        sa.max_score + sa.condition_count as score,
        sa.condition_count::INTEGER,
        sa.all_conditions
    FROM supplement_aggregates sa
    
    UNION ALL
    
    -- Return food recommendations (unchanged)
    SELECT 
        ht.tag_name as health_tag_name,
        ht.description as health_tag_description,
        'food'::TEXT as recommendation_type,
        fr.id as recommendation_id,
        fr.name as recommendation_name,
        jsonb_build_object(
            'description', fr.description,
            'benefits', fr.benefits,
            'nutritional_info', fr.nutritional_info,
            'serving_suggestions', fr.serving_suggestions
        ) as recommendation_details,
        2 as priority,
        1 as score,
        1 as condition_count,
        ARRAY[ht.tag_name] as all_conditions
    FROM health_tags ht
    JOIN food_recommendations fr ON fr.health_tag_id = ht.id
    WHERE EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(ht.triggers) as trigger_obj
        WHERE user_answers ? (trigger_obj->>'question_id')
        AND user_answers->(trigger_obj->>'question_id') ? (trigger_obj->>'answer')
    )
    
    ORDER BY priority ASC, score DESC, condition_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Verification
DO $$
DECLARE
    supplement_count INTEGER;
    recommendation_count INTEGER;
    food_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO supplement_count FROM supplements;
    SELECT COUNT(*) INTO recommendation_count FROM supplement_recommendations;
    SELECT COUNT(*) INTO food_count FROM food_recommendations;
    
    RAISE NOTICE 'Enhanced supplement database update complete:';
    RAISE NOTICE '- Supplements: %', supplement_count;
    RAISE NOTICE '- Supplement Recommendations: %', recommendation_count;
    RAISE NOTICE '- Food Recommendations: %', food_count;
END $$;