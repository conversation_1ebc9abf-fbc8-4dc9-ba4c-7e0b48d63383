import { supabase } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'

export interface SubscriptionInfo {
  hasActiveSubscription: boolean
  subscriptionId?: string
  stripeId?: string
  periodEnd?: string
  isGracePeriod?: boolean
  error?: string
}

export async function checkUserSubscription(userId: string): Promise<SubscriptionInfo> {
  try {
    console.log('🔍 checkUserSubscription: Starting check for userId:', userId)
    console.log('🔍 userId type:', typeof userId, 'length:', userId?.length)
    
    // First, get current user info
    const { data: currentUser, error: authError } = await supabase.auth.getUser()
    console.log('👤 Auth state check:')
    console.log('  - Current logged-in user email:', currentUser.user?.email)
    console.log('  - Current logged-in user ID:', currentUser.user?.id)
    console.log('  - Auth error:', authError)
    console.log('  - User ID match:', currentUser.user?.id === userId)
    
    // Validate user ID format
    if (!userId || typeof userId !== 'string') {
      console.error('❌ Invalid userId provided:', userId)
      return {
        hasActiveSubscription: false,
        error: 'Invalid user ID provided'
      }
    }
    
    // Check if we have a valid auth session
    if (authError) {
      console.error('❌ Auth error detected:', authError)
      return {
        hasActiveSubscription: false,
        error: 'Authentication error: ' + authError.message
      }
    }
    
    if (!currentUser.user) {
      console.error('❌ No authenticated user found')
      return {
        hasActiveSubscription: false,
        error: 'No authenticated user found'
      }
    }
    
    // Warn if user ID mismatch
    if (currentUser.user.id !== userId) {
      console.warn('⚠️ User ID mismatch detected!')
      console.warn('  - Provided userId:', userId)
      console.warn('  - Auth user ID:', currentUser.user.id)
      console.warn('  - This may cause RLS policy failures')
    }
    
    // Try a simple query first to test RLS
    console.log('🧪 Testing RLS access with basic query...')
    const { data: testQuery, error: testError } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(5)
    
    console.log('🧪 Test query result:')
    console.log('  - Data:', testQuery)
    console.log('  - Error:', testError)
    console.log('  - Can access table:', !testError)
    
    // Try with just basic select for user subscriptions
    console.log('📊 Querying subscriptions for userId:', userId)
    const { data: allSubs, error: allError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
    
    console.log('📊 Query results:')
    console.log('  - Subscriptions found:', allSubs?.length || 0)
    console.log('  - Subscription data:', allSubs)
    console.log('  - Query error:', allError)
    
    if (allError) {
      console.error('❌ Database error accessing subscriptions:', allError)
      console.error('  - Error code:', allError.code)
      console.error('  - Error message:', allError.message)
      console.error('  - Error details:', allError.details)
      return {
        hasActiveSubscription: false,
        error: allError.message
      }
    }
    
    // Check for any active subscriptions (including canceled ones within grace period)
    const activeSubscriptions = allSubs?.filter(sub => {
      console.log('  - Checking subscription:', sub.id, 'status:', sub.status, 'period_end:', sub.period_end)
      
      // Active subscriptions are always valid
      if (sub.status === 'active') {
        return true
      }
      
      // Canceled subscriptions are valid until period_end (grace period)
      if (sub.status === 'canceled' && sub.period_end) {
        const periodEnd = new Date(sub.period_end)
        const now = new Date()
        const isWithinGracePeriod = periodEnd > now
        
        console.log('  - Canceled subscription grace period check:')
        console.log('    - Period ends:', periodEnd.toISOString())
        console.log('    - Current time:', now.toISOString())
        console.log('    - Within grace period:', isWithinGracePeriod)
        
        return isWithinGracePeriod
      }
      
      return false
    }) || []
    
    console.log('✅ Active subscriptions found:', activeSubscriptions.length)
    console.log('✅ Active subscription details:', activeSubscriptions)
    
    if (activeSubscriptions.length > 0) {
      const activeSub = activeSubscriptions[0]
      const isGracePeriod = activeSub.status === 'canceled'
      
      console.log('✅ SUBSCRIPTION FOUND - User has valid subscription!')
      console.log('  - Subscription ID:', activeSub.id)
      console.log('  - Stripe ID:', activeSub.stripe_id)
      console.log('  - Status:', activeSub.status)
      console.log('  - Period end:', activeSub.period_end)
      console.log('  - Grace period access:', isGracePeriod)
      
      return {
        hasActiveSubscription: true,
        subscriptionId: activeSub.id,
        stripeId: activeSub.stripe_id,
        periodEnd: activeSub.period_end,
        isGracePeriod
      }
    }
    
    console.log('❌ NO ACTIVE SUBSCRIPTION FOUND')
    console.log('  - Total subscriptions:', allSubs?.length || 0)
    console.log('  - Active subscriptions:', activeSubscriptions.length)
    
    return { hasActiveSubscription: false }
  } catch (error) {
    console.error('💥 Exception in checkUserSubscription:', error)
    console.error('💥 Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return {
      hasActiveSubscription: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export function isLoggedInAndSubscribed(user: User | null, subscriptionInfo?: SubscriptionInfo): boolean {
  return !!(user && subscriptionInfo?.hasActiveSubscription)
}

// Enhanced subscription check with retry logic
export async function checkUserSubscriptionWithRetry(userId: string, maxRetries: number = 3): Promise<SubscriptionInfo> {
  let lastError: string | undefined;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`🔄 Subscription check attempt ${attempt} of ${maxRetries} for user: ${userId}`)
    
    const result = await checkUserSubscription(userId)
    
    if (result.hasActiveSubscription || !result.error) {
      console.log(`✅ Subscription check successful on attempt ${attempt}`)
      return result
    }
    
    lastError = result.error
    console.log(`❌ Subscription check failed on attempt ${attempt}:`, result.error)
    
    if (attempt < maxRetries) {
      const delay = Math.min(1000 * attempt, 3000) // Progressive delay: 1s, 2s, 3s
      console.log(`⏳ Waiting ${delay}ms before retry...`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  console.log(`💥 All subscription check attempts failed. Last error:`, lastError)
  return {
    hasActiveSubscription: false,
    error: lastError || 'Subscription check failed after multiple attempts'
  }
}

export async function requiresPayment(user: User | null): Promise<boolean> {
  if (!user) return true
  
  const subscriptionInfo = await checkUserSubscriptionWithRetry(user.id)
  return !subscriptionInfo.hasActiveSubscription
}

// Utility function to manually sync subscription status (for debugging)
export async function syncSubscriptionStatus(userId: string): Promise<{success: boolean, message: string, data?: any}> {
  try {
    console.log('🔄 Manual subscription sync initiated for user:', userId)
    
    // Check current auth state
    const { data: currentUser, error: authError } = await supabase.auth.getUser()
    if (authError || !currentUser.user) {
      return {
        success: false,
        message: 'Not authenticated'
      }
    }
    
    if (currentUser.user.id !== userId) {
      return {
        success: false,
        message: 'User ID mismatch - can only sync own subscription'
      }
    }
    
    // Force refresh subscription data
    const subscriptionInfo = await checkUserSubscriptionWithRetry(userId, 3)
    
    return {
      success: true,
      message: subscriptionInfo.hasActiveSubscription ? 'Active subscription found' : 'No active subscription found',
      data: subscriptionInfo
    }
    
  } catch (error) {
    console.error('Error syncing subscription status:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Debug function to expose subscription checking on window object (development only)
export function enableSubscriptionDebugging() {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    (window as any).debugSubscription = {
      check: checkUserSubscription,
      checkWithRetry: checkUserSubscriptionWithRetry,
      sync: syncSubscriptionStatus
    }
    console.log('🔧 Subscription debugging enabled. Use window.debugSubscription in console')
  }
}