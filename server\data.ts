// Health conditions and supplement data based on CSV files

export interface HealthCondition {
  name: string
  description: string
  triggers: string[]
}

export interface Supplement {
  name: string
  condition_name: string
  description: string
  benefits: string[]
  side_effects: string[]
  contraindications: string[]
  min_dose: string
  max_dose: string
  dosage: string
  timing: string
  form: string
  with_food: boolean
  interactions: string[]
  pregnancy_safe: boolean
  breastfeeding_safe: boolean
}

export interface FoodRecommendation {
  name: string
  condition_name: string
  description: string
  benefits: string[]
  serving_suggestions: string[]
}

export const HEALTH_CONDITIONS: HealthCondition[] = [
  {
    name: "Low Energy",
    description: "You may benefit from energy-supporting nutrients and lifestyle adjustments",
    triggers: ["Very low or low energy levels", "Poor concentration", "Sugar cravings"]
  },
  {
    name: "Sleep Quality Issues",
    description: "Sleep support may help improve your rest quality and recovery",
    triggers: ["Trouble sleeping", "Not waking up refreshed"]
  },
  {
    name: "Joint Support Needed",
    description: "Joint health support may help reduce discomfort and improve mobility",
    triggers: ["Joint pain or stiffness", "Poor joint flexibility"]
  },
  {
    name: "Physical Activity Support",
    description: "Nutritional support for increasing physical activity and recovery",
    triggers: ["Low physical activity levels", "Poor stamina", "Slow recovery"]
  },
  {
    name: "Stress Management",
    description: "Stress reduction support may help improve overall wellbeing",
    triggers: ["Regular stress experience", "Poor work-life balance"]
  },
  {
    name: "Digestive Health Support",
    description: "Digestive health support may improve nutrient absorption and gut health",
    triggers: ["Poor or fair digestive health"]
  },
  {
    name: "Immune System Support",
    description: "Immune support may help maintain your body's natural defenses",
    triggers: ["Frequent illness", "Poor immunity"]
  },
  {
    name: "Hair & Nail Health",
    description: "Nutritional support for stronger hair and nails",
    triggers: ["Hair thinning", "Poor hair health"]
  },
  {
    name: "Breathing Health",
    description: "Support for respiratory comfort and oxygen use",
    triggers: ["Breathing difficulties"]
  }
]

export const SUPPLEMENTS: Supplement[] = [
  {
    name: "B-Complex Vitamins",
    condition_name: "Low Energy",
    description: "Essential for energy metabolism and nervous system function",
    benefits: ["Supports energy production", "Reduces fatigue", "Improves mental clarity", "Supports metabolism"],
    side_effects: ["Generally well tolerated", "May cause nausea if taken on empty stomach"],
    contraindications: ["None known for healthy adults"],
    min_dose: "1 capsule",
    max_dose: "2 capsules",
    dosage: "1 capsule daily with breakfast",
    timing: "Morning with food",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Iron Bisglycinate",
    condition_name: "Low Energy",
    description: "Highly absorbable form of iron for energy support",
    benefits: ["Prevents anemia", "Supports oxygen transport", "Boosts energy", "Improves concentration"],
    side_effects: ["Less likely to cause stomach upset than other forms"],
    contraindications: ["Hemochromatosis", "Avoid with certain medications"],
    min_dose: "18mg",
    max_dose: "27mg",
    dosage: "18-27mg daily with vitamin C",
    timing: "Between meals on empty stomach",
    form: "Capsule",
    with_food: false,
    interactions: ["Enhances absorption with vitamin C", "Reduces absorption with calcium"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Magnesium Glycinate",
    condition_name: "Sleep Quality Issues",
    description: "Highly absorbable form of magnesium for relaxation and sleep",
    benefits: ["Promotes relaxation", "Improves sleep quality", "Reduces muscle tension", "Supports nervous system"],
    side_effects: ["May cause loose stools in high doses"],
    contraindications: ["Kidney disease", "Heart block"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg before bedtime",
    timing: "30-60 minutes before sleep",
    form: "Capsule",
    with_food: false,
    interactions: ["May enhance effects of blood pressure medications"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Melatonin",
    condition_name: "Sleep Quality Issues",
    description: "Natural hormone that regulates circadian rhythm",
    benefits: ["Regulates sleep-wake cycle", "Improves sleep onset", "Antioxidant properties", "Jet lag recovery"],
    side_effects: ["Drowsiness", "Vivid dreams", "Mild headache"],
    contraindications: ["Autoimmune disorders", "Depression", "Pregnancy"],
    min_dose: "0.5mg",
    max_dose: "3mg",
    dosage: "0.5-3mg 30 minutes before bed",
    timing: "30 minutes before desired sleep time",
    form: "Tablet",
    with_food: false,
    interactions: ["May interact with blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Omega-3 Fish Oil",
    condition_name: "Joint Support Needed",
    description: "Anti-inflammatory support for joints and overall health",
    benefits: ["Reduces inflammation", "Supports joint mobility", "Heart health benefits", "Brain function support"],
    side_effects: ["Fishy aftertaste", "Stomach upset if taken on empty stomach"],
    contraindications: ["Fish allergies", "Blood clotting disorders"],
    min_dose: "1000mg",
    max_dose: "2000mg",
    dosage: "1000-2000mg daily with meals",
    timing: "With lunch or dinner",
    form: "Softgel",
    with_food: true,
    interactions: ["May enhance effects of blood thinners"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Turmeric Curcumin",
    condition_name: "Joint Support Needed",
    description: "Potent anti-inflammatory compound from turmeric root",
    benefits: ["Natural anti-inflammatory", "Joint pain relief", "Antioxidant support", "Immune system support"],
    side_effects: ["Stomach irritation", "May increase bleeding risk"],
    contraindications: ["Gallstones", "Blood clotting disorders"],
    min_dose: "500mg",
    max_dose: "1000mg",
    dosage: "500-1000mg daily with black pepper",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May interact with blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Vitamin D3",
    condition_name: "Physical Activity Support",
    description: "Essential for bone health, muscle function, and immune system",
    benefits: ["Bone health", "Muscle function", "Immune support", "Mood regulation"],
    side_effects: ["Rare", "May cause nausea in very high doses"],
    contraindications: ["Hypercalcemia", "Kidney stones"],
    min_dose: "1000 IU",
    max_dose: "2000 IU",
    dosage: "1000-2000 IU daily",
    timing: "With a meal containing fat",
    form: "Softgel",
    with_food: true,
    interactions: ["Enhances calcium absorption"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Creatine Monohydrate",
    condition_name: "Physical Activity Support",
    description: "Well-researched supplement for strength and power",
    benefits: ["Increases muscle strength", "Improves exercise performance", "Supports muscle recovery", "Brain health benefits"],
    side_effects: ["Initial water weight gain", "Rare stomach upset"],
    contraindications: ["Kidney disease"],
    min_dose: "3g",
    max_dose: "5g",
    dosage: "3-5g daily",
    timing: "Post-workout or anytime",
    form: "Powder",
    with_food: false,
    interactions: ["None known"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Ashwagandha",
    condition_name: "Stress Management",
    description: "Adaptogenic herb for stress management and cortisol balance",
    benefits: ["Reduces cortisol levels", "Improves stress response", "Supports mood", "Enhances energy"],
    side_effects: ["Drowsiness", "Stomach upset", "May lower blood sugar"],
    contraindications: ["Autoimmune disorders", "Thyroid medications"],
    min_dose: "300mg",
    max_dose: "600mg",
    dosage: "300-600mg daily",
    timing: "Morning or evening with food",
    form: "Capsule",
    with_food: true,
    interactions: ["May interact with thyroid medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "L-Theanine",
    condition_name: "Stress Management",
    description: "Amino acid that promotes relaxation without drowsiness",
    benefits: ["Promotes calm alertness", "Reduces anxiety", "Improves focus", "Better sleep quality"],
    side_effects: ["Generally well tolerated", "Rare headache"],
    contraindications: ["None known for healthy adults"],
    min_dose: "100mg",
    max_dose: "200mg",
    dosage: "100-200mg daily",
    timing: "Morning or as needed",
    form: "Capsule",
    with_food: false,
    interactions: ["May enhance effects of caffeine"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Probiotics",
    condition_name: "Digestive Health Support",
    description: "Beneficial bacteria for digestive and immune health",
    benefits: ["Supports gut health", "Improves digestion", "Boosts immune function", "Nutrient absorption"],
    side_effects: ["Initial bloating", "Gas", "Mild stomach upset"],
    contraindications: ["Severely compromised immune system"],
    min_dose: "10 billion CFU",
    max_dose: "50 billion CFU",
    dosage: "10-50 billion CFU daily",
    timing: "With or after meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May reduce effectiveness of antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Digestive Enzymes",
    condition_name: "Digestive Health Support",
    description: "Enzymes that help break down proteins, fats, and carbohydrates",
    benefits: ["Improves nutrient breakdown", "Reduces bloating", "Better digestion", "Nutrient absorption"],
    side_effects: ["Rare stomach upset", "Nausea"],
    contraindications: ["Acute pancreatitis", "Gastric ulcer"],
    min_dose: "1 capsule with meals",
    max_dose: "2 capsules with meals",
    dosage: "1-2 capsules with meals",
    timing: "Beginning of each meal",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  }
]

export const FOOD_RECOMMENDATIONS: FoodRecommendation[] = [
  {
    name: "Iron-Rich Foods",
    condition_name: "Low Energy",
    description: "Foods high in iron like spinach, lean red meat, lentils, and quinoa",
    benefits: ["Prevents anemia", "Supports oxygen transport", "Boosts energy", "Improves concentration"],
    serving_suggestions: ["Pair with vitamin C rich foods for better absorption", "Include in daily meals"]
  },
  {
    name: "Complex Carbohydrates",
    condition_name: "Low Energy",
    description: "Whole grains, sweet potatoes, oats, and brown rice for steady energy",
    benefits: ["Sustained energy release", "Stable blood sugar", "B-vitamin rich", "Fiber for gut health"],
    serving_suggestions: ["Choose whole grain options", "Include in breakfast and lunch"]
  },
  {
    name: "Tart Cherry Juice",
    condition_name: "Sleep Quality Issues",
    description: "Natural source of melatonin for better sleep",
    benefits: ["Natural melatonin", "Improves sleep duration", "Reduces inflammation", "Antioxidant properties"],
    serving_suggestions: ["8oz 1-2 hours before bedtime", "Choose 100% juice without added sugar"]
  },
  {
    name: "Magnesium-Rich Foods",
    condition_name: "Sleep Quality Issues",
    description: "Almonds, spinach, pumpkin seeds, and dark chocolate",
    benefits: ["Promotes relaxation", "Better sleep quality", "Muscle function", "Nervous system support"],
    serving_suggestions: ["Include handful of nuts as evening snack", "Add spinach to dinner"]
  },
  {
    name: "Fatty Fish & Walnuts",
    condition_name: "Joint Support Needed",
    description: "Salmon, sardines, mackerel, and walnuts for omega-3 fatty acids",
    benefits: ["Anti-inflammatory omega-3s", "Joint support", "Heart healthy", "Brain food"],
    serving_suggestions: ["2-3 servings of fatty fish per week", "Handful of walnuts daily"]
  },
  {
    name: "Colorful Berries",
    condition_name: "Joint Support Needed",
    description: "Blueberries, cherries, and strawberries rich in anthocyanins",
    benefits: ["Antioxidant compounds", "Anti-inflammatory", "Vitamin C", "Fiber"],
    serving_suggestions: ["1 cup daily", "Add to breakfast or as snacks", "Frozen options work too"]
  },
  {
    name: "Protein-Rich Foods",
    condition_name: "Physical Activity Support",
    description: "Lean meats, eggs, Greek yogurt, and legumes for muscle support",
    benefits: ["Muscle building", "Recovery support", "Satiety", "Metabolic boost"],
    serving_suggestions: ["Include protein with each meal", "20-30g post-workout"]
  },
  {
    name: "Bananas & Sweet Potatoes",
    condition_name: "Physical Activity Support",
    description: "Natural sources of potassium and complex carbs",
    benefits: ["Natural electrolytes", "Potassium for muscles", "Energy for workouts", "Post-exercise recovery"],
    serving_suggestions: ["Pre-workout snack", "Post-workout recovery food"]
  },
  {
    name: "Green Tea & Dark Chocolate",
    condition_name: "Stress Management",
    description: "Natural stress-reducing compounds and healthy treats",
    benefits: ["L-theanine for calm", "Antioxidants", "Mood support", "Cognitive benefits"],
    serving_suggestions: ["2-3 cups green tea daily", "1oz dark chocolate (70%+ cacao)"]
  },
  {
    name: "Adaptogenic Foods",
    condition_name: "Stress Management",
    description: "Mushrooms, ginseng tea, and maca powder",
    benefits: ["Stress response support", "Energy balance", "Immune support", "Mental clarity"],
    serving_suggestions: ["Add mushrooms to meals", "Ginseng tea between meals", "Maca in smoothies"]
  },
  {
    name: "Fermented Foods",
    condition_name: "Digestive Health Support",
    description: "Yogurt, kefir, sauerkraut, kimchi, and kombucha",
    benefits: ["Natural probiotics", "Digestive enzymes", "Immune support", "Nutrient density"],
    serving_suggestions: ["Include 1-2 servings daily", "Start with small amounts"]
  },
  {
    name: "Fiber-Rich Foods",
    condition_name: "Digestive Health Support",
    description: "Vegetables, fruits, legumes, and whole grains",
    benefits: ["Gut microbiome support", "Regular digestion", "Nutrient absorption", "Satiety"],
    serving_suggestions: ["Aim for 25-35g fiber daily", "Increase gradually with water"]
  },
  {
    name: "Leafy Green Vegetables",
    condition_name: "Low Energy",
    description: "Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods",
    benefits: ["High in folate", "Rich in iron", "Antioxidant properties", "Fiber for gut health"],
    serving_suggestions: ["Include 2-3 cups daily", "Add to smoothies, salads, and cooked dishes"]
  }
]