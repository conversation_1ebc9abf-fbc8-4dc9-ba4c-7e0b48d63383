-- ============================================================================
-- Seed Data - Run this fourth (after schema is complete)
-- ============================================================================

-- Clear existing data (in reverse dependency order)
DELETE FROM recommendations;
DELETE FROM responses;
DELETE FROM reports;
DELETE FROM subscriptions;
DELETE FROM health_tags;
DELETE FROM questions;
-- Don't delete profiles as they may be created by auth triggers

-- ============================================================================
-- Insert Quiz Questions
-- ============================================================================

INSERT INTO questions (id, text, type, options, "order") VALUES
('550e8400-e29b-41d4-a716-446655440001', 'What is your gender?', 'multiple-choice', ARRAY['Male', 'Female', 'Prefer not to say'], 1),
('550e8400-e29b-41d4-a716-446655440002', 'What is your age range?', 'multiple-choice', ARRAY['18-25', '26-35', '36-45', '46-55', '55+'], 2),
('550e8400-e29b-41d4-a716-446655440003', 'How would you describe your energy levels?', 'multiple-choice', ARRAY['Very low', 'Low', 'Moderate', 'High', 'Very high'], 3),
('550e8400-e29b-41d4-a716-446655440004', 'Do you have trouble sleeping?', 'yes-no', ARRAY['Yes', 'No'], 4),
('550e8400-e29b-41d4-a716-446655440005', 'Do you experience joint pain or stiffness?', 'yes-no', ARRAY['Yes', 'No'], 5),
('550e8400-e29b-41d4-a716-446655440006', 'How often do you exercise?', 'multiple-choice', ARRAY['Never', '1-2 times per week', '3-4 times per week', '5+ times per week'], 6),
('550e8400-e29b-41d4-a716-446655440007', 'Do you follow any specific diet?', 'multiple-choice', ARRAY['No specific diet', 'Vegetarian', 'Vegan', 'Keto', 'Paleo', 'Mediterranean'], 7),
('550e8400-e29b-41d4-a716-446655440008', 'Do you experience stress regularly?', 'yes-no', ARRAY['Yes', 'No'], 8),
('550e8400-e29b-41d4-a716-446655440009', 'How would you rate your digestive health?', 'multiple-choice', ARRAY['Poor', 'Fair', 'Good', 'Excellent'], 9),
('550e8400-e29b-41d4-a716-446655440010', 'Do you take any medications regularly?', 'yes-no', ARRAY['Yes', 'No'], 10);

-- ============================================================================
-- Insert Health Tags (must be before recommendations)
-- ============================================================================

INSERT INTO health_tags (id, tag_name, description, triggers) VALUES
('660e8400-e29b-41d4-a716-446655440001', 'Low Energy', 'You may benefit from energy-supporting nutrients and lifestyle adjustments', 
 '[{"question_id": "550e8400-e29b-41d4-a716-446655440003", "answer": "Very low"}, {"question_id": "550e8400-e29b-41d4-a716-446655440003", "answer": "Low"}]'::jsonb),
('660e8400-e29b-41d4-a716-446655440002', 'Sleep Quality Issues', 'Sleep support may help improve your rest quality and recovery', 
 '[{"question_id": "550e8400-e29b-41d4-a716-446655440004", "answer": "Yes"}]'::jsonb),
('660e8400-e29b-41d4-a716-446655440003', 'Joint Support Needed', 'Joint health support may help reduce discomfort and improve mobility', 
 '[{"question_id": "550e8400-e29b-41d4-a716-446655440005", "answer": "Yes"}]'::jsonb),
('660e8400-e29b-41d4-a716-446655440004', 'Physical Activity Support', 'Nutritional support for increasing physical activity and recovery', 
 '[{"question_id": "550e8400-e29b-41d4-a716-446655440006", "answer": "Never"}, {"question_id": "550e8400-e29b-41d4-a716-446655440006", "answer": "1-2 times per week"}]'::jsonb),
('660e8400-e29b-41d4-a716-446655440005', 'Stress Management', 'Stress reduction support may help improve overall wellbeing', 
 '[{"question_id": "550e8400-e29b-41d4-a716-446655440008", "answer": "Yes"}]'::jsonb),
('660e8400-e29b-41d4-a716-446655440006', 'Digestive Health Support', 'Digestive health support may improve nutrient absorption and gut health', 
 '[{"question_id": "550e8400-e29b-41d4-a716-446655440009", "answer": "Poor"}, {"question_id": "550e8400-e29b-41d4-a716-446655440009", "answer": "Fair"}]'::jsonb);

-- ============================================================================
-- Insert Supplement Recommendations
-- ============================================================================

INSERT INTO recommendations (id, health_tag_id, type, name, details) VALUES
-- Low Energy supplements
('770e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'supplement', 'B-Complex Vitamins', 
 '{"dosage": "1 capsule daily with breakfast", "timing": "Morning with food", "benefits": ["Supports energy production", "Reduces fatigue", "Improves mental clarity", "Supports metabolism"], "description": "Essential for energy metabolism and nervous system function"}'::jsonb),
('770e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', 'supplement', 'Iron Bisglycinate', 
 '{"dosage": "18-27mg daily with vitamin C", "timing": "Between meals on empty stomach", "benefits": ["Prevents anemia", "Supports oxygen transport", "Boosts energy", "Improves concentration"], "description": "Highly absorbable form of iron for energy support"}'::jsonb),

-- Sleep support supplements
('770e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440002', 'supplement', 'Magnesium Glycinate', 
 '{"dosage": "200-400mg before bedtime", "timing": "30-60 minutes before sleep", "benefits": ["Promotes relaxation", "Improves sleep quality", "Reduces muscle tension", "Supports nervous system"], "description": "Highly absorbable form of magnesium for relaxation and sleep"}'::jsonb),
('770e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440002', 'supplement', 'Melatonin', 
 '{"dosage": "0.5-3mg 30 minutes before bed", "timing": "30 minutes before desired sleep time", "benefits": ["Regulates sleep-wake cycle", "Improves sleep onset", "Antioxidant properties", "Jet lag recovery"], "description": "Natural hormone that regulates circadian rhythm"}'::jsonb),

-- Joint support supplements
('770e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440003', 'supplement', 'Omega-3 Fish Oil', 
 '{"dosage": "1000-2000mg daily with meals", "timing": "With lunch or dinner", "benefits": ["Reduces inflammation", "Supports joint mobility", "Heart health benefits", "Brain function support"], "description": "Anti-inflammatory support for joints and overall health"}'::jsonb),
('770e8400-e29b-41d4-a716-446655440006', '660e8400-e29b-41d4-a716-446655440003', 'supplement', 'Turmeric Curcumin', 
 '{"dosage": "500-1000mg daily with black pepper", "timing": "With meals", "benefits": ["Natural anti-inflammatory", "Joint pain relief", "Antioxidant support", "Immune system support"], "description": "Potent anti-inflammatory compound from turmeric root"}'::jsonb),

-- Physical activity supplements
('770e8400-e29b-41d4-a716-446655440007', '660e8400-e29b-41d4-a716-446655440004', 'supplement', 'Vitamin D3', 
 '{"dosage": "1000-2000 IU daily", "timing": "With a meal containing fat", "benefits": ["Bone health", "Muscle function", "Immune support", "Mood regulation"], "description": "Essential for bone health, muscle function, and immune system"}'::jsonb),
('770e8400-e29b-41d4-a716-446655440008', '660e8400-e29b-41d4-a716-446655440004', 'supplement', 'Creatine Monohydrate', 
 '{"dosage": "3-5g daily", "timing": "Post-workout or anytime", "benefits": ["Increases muscle strength", "Improves exercise performance", "Supports muscle recovery", "Brain health benefits"], "description": "Well-researched supplement for strength and power"}'::jsonb),

-- Stress management supplements
('770e8400-e29b-41d4-a716-446655440009', '660e8400-e29b-41d4-a716-446655440005', 'supplement', 'Ashwagandha', 
 '{"dosage": "300-600mg daily", "timing": "Morning or evening with food", "benefits": ["Reduces cortisol levels", "Improves stress response", "Supports mood", "Enhances energy"], "description": "Adaptogenic herb for stress management and cortisol balance"}'::jsonb),
('770e8400-e29b-41d4-a716-446655440010', '660e8400-e29b-41d4-a716-446655440005', 'supplement', 'L-Theanine', 
 '{"dosage": "100-200mg daily", "timing": "Morning or as needed", "benefits": ["Promotes calm alertness", "Reduces anxiety", "Improves focus", "Better sleep quality"], "description": "Amino acid that promotes relaxation without drowsiness"}'::jsonb),

-- Digestive health supplements
('770e8400-e29b-41d4-a716-446655440011', '660e8400-e29b-41d4-a716-446655440006', 'supplement', 'Probiotics', 
 '{"dosage": "10-50 billion CFU daily", "timing": "With or after meals", "benefits": ["Supports gut health", "Improves digestion", "Boosts immune function", "Nutrient absorption"], "description": "Beneficial bacteria for digestive and immune health"}'::jsonb),
('770e8400-e29b-41d4-a716-446655440012', '660e8400-e29b-41d4-a716-446655440006', 'supplement', 'Digestive Enzymes', 
 '{"dosage": "1-2 capsules with meals", "timing": "Beginning of each meal", "benefits": ["Improves nutrient breakdown", "Reduces bloating", "Better digestion", "Nutrient absorption"], "description": "Enzymes that help break down proteins, fats, and carbohydrates"}'::jsonb);

-- ============================================================================
-- Insert Food Recommendations
-- ============================================================================

INSERT INTO recommendations (id, health_tag_id, type, name, details) VALUES
-- Low Energy foods
('880e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'food', 'Iron-Rich Foods', 
 '{"benefits": ["Prevents anemia", "Supports oxygen transport", "Boosts energy", "Improves concentration"], "description": "Foods high in iron like spinach, lean red meat, lentils, and quinoa"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', 'food', 'Complex Carbohydrates', 
 '{"benefits": ["Sustained energy release", "Stable blood sugar", "B-vitamin rich", "Fiber for gut health"], "description": "Whole grains, sweet potatoes, oats, and brown rice for steady energy"}'::jsonb),

-- Sleep support foods
('880e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440002', 'food', 'Tart Cherry Juice', 
 '{"benefits": ["Natural melatonin", "Improves sleep duration", "Reduces inflammation", "Antioxidant properties"], "description": "Natural source of melatonin for better sleep"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440002', 'food', 'Magnesium-Rich Foods', 
 '{"benefits": ["Promotes relaxation", "Better sleep quality", "Muscle function", "Nervous system support"], "description": "Almonds, spinach, pumpkin seeds, and dark chocolate"}'::jsonb),

-- Joint support foods
('880e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440003', 'food', 'Fatty Fish & Walnuts', 
 '{"benefits": ["Anti-inflammatory omega-3s", "Joint support", "Heart healthy", "Brain food"], "description": "Salmon, sardines, mackerel, and walnuts for omega-3 fatty acids"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440006', '660e8400-e29b-41d4-a716-446655440003', 'food', 'Colorful Berries', 
 '{"benefits": ["Antioxidant compounds", "Anti-inflammatory", "Vitamin C", "Fiber"], "description": "Blueberries, cherries, and strawberries rich in anthocyanins"}'::jsonb),

-- Physical activity foods
('880e8400-e29b-41d4-a716-446655440007', '660e8400-e29b-41d4-a716-446655440004', 'food', 'Protein-Rich Foods', 
 '{"benefits": ["Muscle building", "Recovery support", "Satiety", "Metabolic boost"], "description": "Lean meats, eggs, Greek yogurt, and legumes for muscle support"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440008', '660e8400-e29b-41d4-a716-446655440004', 'food', 'Bananas & Sweet Potatoes', 
 '{"benefits": ["Natural electrolytes", "Potassium for muscles", "Energy for workouts", "Post-exercise recovery"], "description": "Natural sources of potassium and complex carbs"}'::jsonb),

-- Stress management foods
('880e8400-e29b-41d4-a716-446655440009', '660e8400-e29b-41d4-a716-446655440005', 'food', 'Green Tea & Dark Chocolate', 
 '{"benefits": ["L-theanine for calm", "Antioxidants", "Mood support", "Cognitive benefits"], "description": "Natural stress-reducing compounds and healthy treats"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440010', '660e8400-e29b-41d4-a716-446655440005', 'food', 'Adaptogenic Foods', 
 '{"benefits": ["Stress response support", "Energy balance", "Immune support", "Mental clarity"], "description": "Mushrooms, ginseng tea, and maca powder"}'::jsonb),

-- Digestive health foods
('880e8400-e29b-41d4-a716-446655440011', '660e8400-e29b-41d4-a716-446655440006', 'food', 'Fermented Foods', 
 '{"benefits": ["Natural probiotics", "Digestive enzymes", "Immune support", "Nutrient density"], "description": "Yogurt, kefir, sauerkraut, kimchi, and kombucha"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440012', '660e8400-e29b-41d4-a716-446655440006', 'food', 'Fiber-Rich Foods', 
 '{"benefits": ["Gut microbiome support", "Regular digestion", "Nutrient absorption", "Satiety"], "description": "Vegetables, fruits, legumes, and whole grains"}'::jsonb),

-- General wellness foods (for all tags)
('880e8400-e29b-41d4-a716-446655440013', '660e8400-e29b-41d4-a716-446655440001', 'food', 'Leafy Green Vegetables', 
 '{"benefits": ["High in folate", "Rich in iron", "Antioxidant properties", "Fiber for gut health"], "description": "Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440014', '660e8400-e29b-41d4-a716-446655440002', 'food', 'Leafy Green Vegetables', 
 '{"benefits": ["High in folate", "Rich in iron", "Antioxidant properties", "Fiber for gut health"], "description": "Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440015', '660e8400-e29b-41d4-a716-446655440003', 'food', 'Leafy Green Vegetables', 
 '{"benefits": ["High in folate", "Rich in iron", "Antioxidant properties", "Fiber for gut health"], "description": "Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440016', '660e8400-e29b-41d4-a716-446655440004', 'food', 'Leafy Green Vegetables', 
 '{"benefits": ["High in folate", "Rich in iron", "Antioxidant properties", "Fiber for gut health"], "description": "Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440017', '660e8400-e29b-41d4-a716-446655440005', 'food', 'Leafy Green Vegetables', 
 '{"benefits": ["High in folate", "Rich in iron", "Antioxidant properties", "Fiber for gut health"], "description": "Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods"}'::jsonb),
('880e8400-e29b-41d4-a716-446655440018', '660e8400-e29b-41d4-a716-446655440006', 'food', 'Leafy Green Vegetables', 
 '{"benefits": ["High in folate", "Rich in iron", "Antioxidant properties", "Fiber for gut health"], "description": "Spinach, kale, arugula, and Swiss chard - nutrient-dense foundation foods"}'::jsonb);

-- ============================================================================
-- Verification Queries
-- ============================================================================

-- Check that all data was inserted correctly
DO $$
DECLARE
    question_count INTEGER;
    health_tag_count INTEGER;
    recommendation_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO question_count FROM questions;
    SELECT COUNT(*) INTO health_tag_count FROM health_tags;
    SELECT COUNT(*) INTO recommendation_count FROM recommendations;
    
    RAISE NOTICE 'Data seeding complete:';
    RAISE NOTICE '- Questions: %', question_count;
    RAISE NOTICE '- Health Tags: %', health_tag_count;
    RAISE NOTICE '- Recommendations: %', recommendation_count;
    
    IF question_count != 10 THEN
        RAISE WARNING 'Expected 10 questions, got %', question_count;
    END IF;
    
    IF health_tag_count != 6 THEN
        RAISE WARNING 'Expected 6 health tags, got %', health_tag_count;
    END IF;
    
    IF recommendation_count < 20 THEN
        RAISE WARNING 'Expected at least 20 recommendations, got %', recommendation_count;
    END IF;
END $$;