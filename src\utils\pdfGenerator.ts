import jsPDF from 'jspdf'

interface HealthTag {
  name: string
  description: string
}

interface Recommendation {
  type: 'supplement' | 'food'
  name: string
  description: string
  benefits: string[]
  dosage?: string
  timing?: string
  // Enhanced supplement data
  side_effects?: string[]
  contraindications?: string[]
  max_dose?: string
  form?: string
  with_food?: boolean
  interactions?: string[]
  pregnancy_safe?: boolean
  breastfeeding_safe?: boolean
  rationale?: string
  priority_score?: number
  all_conditions?: string[]
  // Enhanced food data
  nutritional_info?: any
  serving_suggestions?: string[]
}

interface PDFData {
  healthTags: HealthTag[]
  recommendations: Recommendation[]
  answers: Record<string, string>
}

export function generateHealthReportPDF(data: PDFData): void {
  const { healthTags, recommendations } = data
  // Sort supplements by priority score (higher score = higher priority)
  const supplements = recommendations
    .filter(r => r.type === 'supplement')
    .sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0))
  const foods = recommendations.filter(r => r.type === 'food')

  // Create new PDF document
  const doc = new jsPDF()
  const pageWidth = doc.internal.pageSize.getWidth()
  const pageHeight = doc.internal.pageSize.getHeight()
  let yPosition = 20

  // Helper function to add text with word wrapping
  const addWrappedText = (text: string, x: number, y: number, maxWidth: number, fontSize: number = 10): number => {
    doc.setFontSize(fontSize)
    const lines = doc.splitTextToSize(text, maxWidth)
    doc.text(lines, x, y)
    return y + (lines.length * fontSize * 0.4)
  }

  // Helper function to convert hex color to RGB
  const hexToRgb = (hex: string): [number, number, number] => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result 
      ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)]
      : [240, 248, 255] // default light blue
  }

  // Helper function to add colored background box
  const addColoredBox = (x: number, y: number, width: number, height: number, color: string): void => {
    const [r, g, b] = hexToRgb(color)
    doc.setFillColor(r, g, b)
    doc.rect(x, y, width, height, 'F')
  }

  // Helper function to add section header with background
  const addSectionHeader = (title: string, y: number, color: string = '#f0f8ff'): number => {
    addColoredBox(15, y - 5, pageWidth - 30, 15, color)
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(0, 0, 0)
    doc.text(title, 20, y + 5)
    return y + 20
  }

  // Helper function to add warning box
  const addWarningBox = (text: string, y: number): number => {
    const boxHeight = 25
    addColoredBox(15, y - 5, pageWidth - 30, boxHeight, '#fff3cd')
    doc.setDrawColor(255, 193, 7)
    doc.setLineWidth(1)
    doc.rect(15, y - 5, pageWidth - 30, boxHeight)
    
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(133, 100, 4)
    doc.text('⚠️ IMPORTANT:', 20, y + 3)
    doc.setFont('helvetica', 'normal')
    return addWrappedText(text, 20, y + 8, pageWidth - 50, 9) + 10
  }

  // Helper function to add danger box for contraindications
  const addDangerBox = (title: string, items: string[], y: number): number => {
    if (items.length === 0) return y
    
    const estimatedHeight = 15 + (items.length * 8)
    addColoredBox(15, y - 5, pageWidth - 30, estimatedHeight, '#f8d7da')
    doc.setDrawColor(220, 53, 69)
    doc.setLineWidth(1)
    doc.rect(15, y - 5, pageWidth - 30, estimatedHeight)
    
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(114, 28, 36)
    doc.text(`🚫 ${title}`, 20, y + 3)
    yPosition = y + 8
    
    doc.setFontSize(9)
    doc.setFont('helvetica', 'normal')
    items.forEach((item) => {
      yPosition = addWrappedText(`• ${item}`, 25, yPosition, pageWidth - 55, 9)
      yPosition += 2
    })
    
    return yPosition + 10
  }

  // Helper function to add safety indicators
  const addSafetyIndicators = (supplement: Recommendation, y: number): number => {
    if (supplement.pregnancy_safe === false || supplement.breastfeeding_safe === false) {
      const warnings = []
      if (supplement.pregnancy_safe === false) warnings.push('Not safe during pregnancy')
      if (supplement.breastfeeding_safe === false) warnings.push('Not safe while breastfeeding')
      
      addColoredBox(15, y - 5, pageWidth - 30, 15, '#fff3cd')
      doc.setFontSize(9)
      doc.setFont('helvetica', 'bold')
      doc.setTextColor(133, 100, 4)
      doc.text(`⚠️ ${warnings.join(' • ')}`, 20, y + 2)
      return y + 15
    }
    return y
  }

  // Helper function to check if we need a new page
  const checkNewPage = (requiredSpace: number): number => {
    if (yPosition + requiredSpace > pageHeight - 20) {
      doc.addPage()
      return 20
    }
    return yPosition
  }

  // Enhanced Header with styling
  addColoredBox(0, 0, pageWidth, 35, '#1e3a8a')
  
  doc.setFontSize(24)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(255, 255, 255)
  doc.text('Your Personalized Health Report', pageWidth / 2, 20, { align: 'center' })
  
  doc.setFontSize(12)
  doc.setFont('helvetica', 'normal')
  doc.setTextColor(200, 200, 200)
  doc.text('Generated by LifeSupplier', pageWidth / 2, 30, { align: 'center' })
  
  yPosition = 50

  // Date with better formatting
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
  
  addColoredBox(15, yPosition - 5, pageWidth - 30, 15, '#f8f9fa')
  doc.setFontSize(10)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(0, 0, 0)
  doc.text(`📅 Report Generated: ${currentDate}`, 20, yPosition + 5)
  yPosition += 25

  // Executive Summary
  yPosition = checkNewPage(30)
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('Executive Summary', 20, yPosition)
  yPosition += 10

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  let summaryText = ''
  if (healthTags.length > 0) {
    summaryText = `Based on your quiz responses, we identified ${healthTags.length} key health area${healthTags.length > 1 ? 's' : ''} that could benefit from targeted nutritional support. This report provides personalized supplement and food recommendations to help you optimize your health and wellbeing.`
  } else {
    summaryText = 'Based on your responses, you have a solid health foundation. This report provides general wellness recommendations to help you maintain and optimize your health.'
  }
  yPosition = addWrappedText(summaryText, 20, yPosition, pageWidth - 40, 10)
  yPosition += 15

  // Health Areas Section
  if (healthTags.length > 0) {
    yPosition = checkNewPage(40)
    yPosition = addSectionHeader('Health Areas to Focus On', yPosition, '#e8f5e8')

    healthTags.forEach((tag, index) => {
      yPosition = checkNewPage(25)
      
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.setTextColor(0, 100, 0)
      doc.text(`${index + 1}. ${tag.name}`, 25, yPosition)
      yPosition += 8

      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      doc.setTextColor(0, 0, 0)
      yPosition = addWrappedText(tag.description, 25, yPosition, pageWidth - 50, 10)
      yPosition += 10
    })
  }

  // Personalized Rationale Section
  if (supplements.length > 0) {
    yPosition = checkNewPage(60)
    yPosition = addSectionHeader('🎯 Why These Supplements Were Chosen for You', yPosition, '#e8f5e8')
    
    supplements.slice(0, 3).forEach((supplement, index) => {
      if (supplement.rationale || supplement.all_conditions) {
        yPosition = checkNewPage(30)
        
        // Priority indicator
        const priorityColor = supplement.priority_score && supplement.priority_score > 7 ? '#c8e6c9' : 
                             supplement.priority_score && supplement.priority_score > 4 ? '#fff3e0' : '#f3e5f5'
        const priorityLabel = supplement.priority_score && supplement.priority_score > 7 ? 'HIGH PRIORITY' :
                             supplement.priority_score && supplement.priority_score > 4 ? 'MEDIUM PRIORITY' : 'LOW PRIORITY'
        
        addColoredBox(20, yPosition - 5, pageWidth - 40, 12, priorityColor)
        doc.setFontSize(11)
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(25, 118, 210)
        doc.text(`${index + 1}. ${supplement.name}`, 25, yPosition + 3)
        
        doc.setFontSize(8)
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(100, 100, 100)
        doc.text(priorityLabel, pageWidth - 80, yPosition + 3)
        yPosition += 15
        
        if (supplement.rationale) {
          doc.setFontSize(10)
          doc.setFont('helvetica', 'normal')
          doc.setTextColor(0, 0, 0)
          yPosition = addWrappedText(supplement.rationale, 25, yPosition, pageWidth - 50, 10)
          yPosition += 5
        }
        
        if (supplement.all_conditions && supplement.all_conditions.length > 0) {
          doc.setFontSize(9)
          doc.setFont('helvetica', 'bold')
          doc.setTextColor(76, 175, 80)
          doc.text('Addresses:', 25, yPosition)
          yPosition += 5
          
          supplement.all_conditions.forEach((condition) => {
            doc.setFont('helvetica', 'normal')
            doc.setTextColor(0, 0, 0)
            yPosition = addWrappedText(`• ${condition}`, 30, yPosition, pageWidth - 55, 9)
            yPosition += 2
          })
        }
        
        yPosition += 10
      }
    })
    
    if (supplements.length > 3) {
      doc.setFontSize(9)
      doc.setFont('helvetica', 'italic')
      doc.setTextColor(108, 117, 125)
      doc.text(`+ ${supplements.length - 3} additional supplements detailed below`, 25, yPosition)
      yPosition += 15
    }
  }

  // Start Smart: One Supplement at a Time Section
  yPosition = checkNewPage(100)
  yPosition = addSectionHeader('🎯 Start Smart: One Supplement at a Time', yPosition, '#fff2e6')
  
  yPosition = addWarningBox('Begin with ONE supplement only. Wait 5-7 days before adding another. This helps you identify what works and avoid adverse reactions.', yPosition)
  
  const startSmartSteps = [
    {
      title: 'Week 1: Choose Your First Supplement',
      content: 'Start with the supplement that addresses your most pressing health concern. Take it at the recommended time for 5-7 days while monitoring how you feel.'
    },
    {
      title: 'Days 5-7: Assess & Document',
      content: 'Note any changes in energy, sleep, digestion, or overall well-being. If you experience any negative effects, discontinue and consult a healthcare provider.'
    },
    {
      title: 'Week 2: Add Second Supplement (if tolerated)',
      content: 'If the first supplement is well-tolerated, you may add a second one. Continue this gradual approach throughout the month.'
    },
    {
      title: 'Important Timing Rules',
      content: '• Take fat-soluble vitamins (A, D, E, K) with meals containing healthy fats\n• Take B vitamins and vitamin C in the morning\n• Take magnesium and calming supplements in the evening\n• Separate iron from calcium by at least 2 hours'
    }
  ]

  startSmartSteps.forEach((step) => {
    yPosition = checkNewPage(30)
    
    doc.setFontSize(11)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(204, 102, 0)
    doc.text(step.title, 25, yPosition)
    yPosition += 8

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.setTextColor(0, 0, 0)
    yPosition = addWrappedText(step.content, 25, yPosition, pageWidth - 50, 10)
    yPosition += 12
  })

  // Supplement Recommendations
  if (supplements.length > 0) {
    yPosition = checkNewPage(40)
    yPosition = addSectionHeader('💊 Your Supplement Recommendations', yPosition, '#f0f8ff')

    supplements.forEach((supplement, index) => {
      yPosition = checkNewPage(120)
      
      // Priority-based header color
      const priorityColor = supplement.priority_score && supplement.priority_score > 7 ? '#c8e6c9' : 
                           supplement.priority_score && supplement.priority_score > 4 ? '#fff3e0' : '#f3e5f5'
      
      // Add supplement box with priority-based border
      addColoredBox(20, yPosition - 5, pageWidth - 40, 8, priorityColor)
      
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.setTextColor(25, 118, 210)
      doc.text(`${index + 1}. ${supplement.name}`, 25, yPosition)
      
      // Add form and priority indicator
      if (supplement.form || supplement.priority_score) {
        doc.setFontSize(8)
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(100, 100, 100)
        const extraInfo = []
        if (supplement.form) extraInfo.push(supplement.form)
        if (supplement.priority_score && supplement.priority_score > 7) extraInfo.push('HIGH PRIORITY')
        else if (supplement.priority_score && supplement.priority_score > 4) extraInfo.push('MEDIUM PRIORITY')
        doc.text(extraInfo.join(' • '), pageWidth - 80, yPosition)
      }
      yPosition += 12

      // Safety indicators
      yPosition = addSafetyIndicators(supplement, yPosition)

      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      doc.setTextColor(0, 0, 0)
      yPosition = addWrappedText(supplement.description, 25, yPosition, pageWidth - 50, 10)
      yPosition += 8

      // Enhanced dosage information
      if (supplement.dosage || supplement.max_dose || supplement.with_food !== undefined) {
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(76, 175, 80)
        doc.text('💊 Dosage Information:', 25, yPosition)
        yPosition += 6
        
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        
        if (supplement.dosage) {
          yPosition = addWrappedText(`Starting dose: ${supplement.dosage}`, 30, yPosition, pageWidth - 55, 10)
          yPosition += 3
        }
        
        if (supplement.max_dose) {
          yPosition = addWrappedText(`Maximum safe dose: ${supplement.max_dose}`, 30, yPosition, pageWidth - 55, 10)
          yPosition += 3
        }
        
        if (supplement.with_food !== undefined) {
          const foodAdvice = supplement.with_food ? 'Take with food' : 'Take on empty stomach'
          yPosition = addWrappedText(`${foodAdvice}`, 30, yPosition, pageWidth - 55, 10)
          yPosition += 3
        }
        
        yPosition += 5
      }

      if (supplement.timing) {
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(255, 152, 0)
        doc.text('⏰ Best Time to Take:', 25, yPosition)
        yPosition += 6
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        yPosition = addWrappedText(supplement.timing, 30, yPosition, pageWidth - 55, 10)
        yPosition += 8
      }

      doc.setFont('helvetica', 'bold')
      doc.setTextColor(156, 39, 176)
      doc.text('✨ Key Benefits:', 25, yPosition)
      yPosition += 5

      supplement.benefits.forEach((benefit) => {
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        yPosition = addWrappedText(`• ${benefit}`, 30, yPosition, pageWidth - 55, 10)
        yPosition += 3
      })
      yPosition += 8

      // Add contraindications if present
      if (supplement.contraindications && supplement.contraindications.length > 0) {
        yPosition = addDangerBox('DO NOT TAKE IF:', supplement.contraindications, yPosition)
      }

      // Add side effects if present
      if (supplement.side_effects && supplement.side_effects.length > 0) {
        yPosition = checkNewPage(20)
        doc.setFontSize(9)
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(255, 152, 0)
        doc.text('⚠️ Possible Side Effects:', 25, yPosition)
        yPosition += 5
        
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        supplement.side_effects.forEach((effect) => {
          yPosition = addWrappedText(`• ${effect}`, 30, yPosition, pageWidth - 55, 9)
          yPosition += 2
        })
        yPosition += 5
      }

      // Add interactions if present
      if (supplement.interactions && supplement.interactions.length > 0) {
        yPosition = checkNewPage(20)
        doc.setFontSize(9)
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(198, 40, 40)
        doc.text('🚨 Drug Interactions:', 25, yPosition)
        yPosition += 5
        
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        supplement.interactions.forEach((interaction) => {
          yPosition = addWrappedText(`• ${interaction}`, 30, yPosition, pageWidth - 55, 9)
          yPosition += 2
        })
        yPosition += 5
      }
      
      yPosition += 10
    })
  }

  // Food Recommendations
  if (foods.length > 0) {
    yPosition = checkNewPage(40)
    yPosition = addSectionHeader('🥗 Nourishing Food Recommendations', yPosition, '#f1f8e9')

    foods.forEach((food, index) => {
      yPosition = checkNewPage(80)
      
      // Add food box with border
      addColoredBox(20, yPosition - 5, pageWidth - 40, 5, '#e8f5e8')
      
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.setTextColor(56, 142, 60)
      doc.text(`${index + 1}. ${food.name}`, 25, yPosition)
      yPosition += 10

      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      doc.setTextColor(0, 0, 0)
      yPosition = addWrappedText(food.description, 25, yPosition, pageWidth - 50, 10)
      yPosition += 8

      // Nutritional information if available
      if (food.nutritional_info) {
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(139, 195, 74)
        doc.text('📊 Nutritional Profile:', 25, yPosition)
        yPosition += 5
        
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        if (typeof food.nutritional_info === 'object') {
          Object.entries(food.nutritional_info).forEach(([key, value]) => {
            yPosition = addWrappedText(`• ${key}: ${value}`, 30, yPosition, pageWidth - 55, 9)
            yPosition += 2
          })
        } else {
          yPosition = addWrappedText(String(food.nutritional_info), 30, yPosition, pageWidth - 55, 9)
          yPosition += 2
        }
        yPosition += 5
      }

      doc.setFont('helvetica', 'bold')
      doc.setTextColor(139, 195, 74)
      doc.text('🌱 Health Benefits:', 25, yPosition)
      yPosition += 5

      food.benefits.forEach((benefit) => {
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        yPosition = addWrappedText(`• ${benefit}`, 30, yPosition, pageWidth - 55, 10)
        yPosition += 3
      })
      yPosition += 5

      // Serving suggestions if available
      if (food.serving_suggestions && food.serving_suggestions.length > 0) {
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(255, 152, 0)
        doc.text('👨‍🍳 How to Enjoy:', 25, yPosition)
        yPosition += 5
        
        food.serving_suggestions.forEach((suggestion) => {
          doc.setFont('helvetica', 'normal')
          doc.setTextColor(0, 0, 0)
          yPosition = addWrappedText(`• ${suggestion}`, 30, yPosition, pageWidth - 55, 9)
          yPosition += 3
        })
        yPosition += 5
      }
      
      yPosition += 10
    })
  }

  // Shopping Checklist Section
  if (supplements.length > 0 || foods.length > 0) {
    yPosition = checkNewPage(100)
    yPosition = addSectionHeader('🛒 Your Shopping Checklist', yPosition, '#fff8e1')
    
    if (supplements.length > 0) {
      doc.setFontSize(14)
      doc.setFont('helvetica', 'bold')
      doc.setTextColor(25, 118, 210)
      doc.text('Supplements to Purchase:', 25, yPosition)
      yPosition += 10
      
      supplements.forEach((supplement) => {
        yPosition = checkNewPage(15)
        
        // Priority indicator
        const priorityEmoji = supplement.priority_score && supplement.priority_score > 7 ? '🔴' :
                             supplement.priority_score && supplement.priority_score > 4 ? '🟡' : '🟢'
        
        doc.setFontSize(10)
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        
        let checklistText = `☐ ${priorityEmoji} ${supplement.name}`
        if (supplement.form) checklistText += ` (${supplement.form})`
        if (supplement.dosage) checklistText += ` - ${supplement.dosage}`
        
        yPosition = addWrappedText(checklistText, 30, yPosition, pageWidth - 55, 10)
        yPosition += 3
      })
      yPosition += 10
    }
    
    if (foods.length > 0) {
      doc.setFontSize(14)
      doc.setFont('helvetica', 'bold')
      doc.setTextColor(56, 142, 60)
      doc.text('Foods to Add to Your Grocery List:', 25, yPosition)
      yPosition += 10
      
      foods.forEach((food) => {
        yPosition = checkNewPage(10)
        
        doc.setFontSize(10)
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        yPosition = addWrappedText(`☐ ${food.name}`, 30, yPosition, pageWidth - 55, 10)
        yPosition += 3
      })
      yPosition += 10
    }
    
    // Shopping tips
    addColoredBox(15, yPosition - 5, pageWidth - 30, 35, '#e8f5e8')
    doc.setFontSize(11)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(27, 94, 32)
    doc.text('💡 Smart Shopping Tips:', 20, yPosition + 3)
    yPosition += 10
    
    const shoppingTips = [
      'Start with HIGH PRIORITY (🔴) supplements first',
      'Look for third-party tested supplements (USP, NSF marks)',
      'Buy from reputable retailers or directly from manufacturers',
      'Check expiration dates and storage requirements',
      'Consider starting with smaller sizes to test tolerance'
    ]
    
    doc.setFontSize(9)
    doc.setFont('helvetica', 'normal')
    doc.setTextColor(0, 0, 0)
    shoppingTips.forEach((tip) => {
      yPosition = addWrappedText(`• ${tip}`, 25, yPosition, pageWidth - 50, 9)
      yPosition += 3
    })
    yPosition += 15
  }

  // 30-Day Implementation Schedule
  yPosition = checkNewPage(100)
  yPosition = addSectionHeader('📅 Your 30-Day Success Plan', yPosition, '#fce4ec')
  
  const weeklyPlan = [
    {
      week: 'Week 1: Foundation',
      focus: 'Start with your highest priority supplement',
      daily: [
        'Take your first supplement at the recommended time',
        'Journal how you feel each day (energy, sleep, mood)',
        'Note any side effects or changes',
        'Maintain your current diet and exercise routine'
      ]
    },
    {
      week: 'Week 2: Assessment',
      focus: 'Evaluate tolerance and effectiveness',
      daily: [
        'Continue with your first supplement',
        'Review your journal entries from week 1',
        'If well-tolerated, prepare to add second supplement',
        'Begin incorporating recommended foods gradually'
      ]
    },
    {
      week: 'Week 3: Expansion',
      focus: 'Add second supplement if appropriate',
      daily: [
        'Add your second supplement if first is well-tolerated',
        'Continue journaling effects of both supplements',
        'Focus on establishing consistent timing',
        'Increase recommended food intake'
      ]
    },
    {
      week: 'Week 4: Optimization',
      focus: 'Fine-tune your routine',
      daily: [
        'Continue current supplement routine',
        'Add third supplement if desired and well-tolerated',
        'Assess overall progress and effectiveness',
        'Plan for month 2 adjustments'
      ]
    }
  ]

  weeklyPlan.forEach((week) => {
    yPosition = checkNewPage(50)
    
    // Week header with colored background
    addColoredBox(20, yPosition - 5, pageWidth - 40, 12, '#f3e5f5')
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(142, 36, 170)
    doc.text(week.week, 25, yPosition + 3)
    yPosition += 15
    
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(0, 0, 0)
    doc.text(`Focus: ${week.focus}`, 25, yPosition)
    yPosition += 8
    
    doc.setFont('helvetica', 'normal')
    doc.text('Daily Actions:', 25, yPosition)
    yPosition += 5
    
    week.daily.forEach((action) => {
      yPosition = addWrappedText(`• ${action}`, 30, yPosition, pageWidth - 55, 9)
      yPosition += 3
    })
    
    yPosition += 10
  })
  
  // Success tips
  yPosition = checkNewPage(40)
  addColoredBox(15, yPosition - 5, pageWidth - 30, 35, '#e8f5e8')
  doc.setFontSize(11)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(27, 94, 32)
  doc.text('💡 Success Tips for Your 30-Day Journey:', 20, yPosition + 3)
  yPosition += 10
  
  const successTips = [
    'Set phone reminders for supplement timing',
    'Keep a simple 1-5 rating scale for daily energy/mood',
    'Take progress photos if relevant to your goals',
    'Connect with a buddy for accountability'
  ]
  
  doc.setFontSize(9)
  doc.setFont('helvetica', 'normal')
  doc.setTextColor(0, 0, 0)
  successTips.forEach((tip) => {
    yPosition = addWrappedText(`• ${tip}`, 25, yPosition, pageWidth - 50, 9)
    yPosition += 3
  })

  // Lifestyle Integration Tips
  yPosition = checkNewPage(80)
  yPosition = addSectionHeader('🌟 Making It Stick: Lifestyle Integration', yPosition, '#fff8e1')
  
  const lifestyleTips = [
    {
      title: 'Build Sustainable Habits',
      tips: [
        'Link supplements to existing habits (e.g., take with morning coffee)',
        'Start with just 2-3 changes at once - don\'t overwhelm yourself',
        'Use the "2-minute rule" - make it easy to start',
        'Celebrate small wins to build momentum'
      ]
    },
    {
      title: 'Meal Planning for Success',
      tips: [
        'Prep recommended foods in bulk on Sundays',
        'Keep healthy snacks visible and accessible',
        'Plan meals around your supplement timing',
        'Try one new healthy recipe per week'
      ]
    },
    {
      title: 'Creating Your Environment',
      tips: [
        'Place supplements where you\'ll see them daily',
        'Remove or hide foods that don\'t support your goals',
        'Stock your kitchen with recommended ingredients',
        'Create a dedicated space for health tracking'
      ]
    }
  ]

  lifestyleTips.forEach((section) => {
    yPosition = checkNewPage(35)
    
    doc.setFontSize(11)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(255, 143, 0)
    doc.text(section.title, 25, yPosition)
    yPosition += 8
    
    section.tips.forEach((tip) => {
      doc.setFontSize(9)
      doc.setFont('helvetica', 'normal')
      doc.setTextColor(0, 0, 0)
      yPosition = addWrappedText(`• ${tip}`, 30, yPosition, pageWidth - 55, 9)
      yPosition += 3
    })
    yPosition += 8
  })

  // Progress Tracking Section
  yPosition = checkNewPage(100)
  yPosition = addSectionHeader('📊 Track Your Progress', yPosition, '#f3e5f5')
  
  const trackingGuidelines = [
    {
      category: 'Daily Tracking (Rate 1-5)',
      items: ['Energy levels', 'Sleep quality', 'Mood/mental clarity', 'Digestion', 'Overall well-being']
    },
    {
      category: 'Weekly Check-ins',
      items: ['Weight/measurements (if relevant)', 'Exercise performance', 'Skin appearance', 'Stress levels', 'Supplement adherence']
    },
    {
      category: 'Monthly Reviews',
      items: ['Overall progress assessment', 'Goal adjustment if needed', 'Supplement routine optimization', 'New symptoms or improvements', 'Cost-benefit analysis']
    }
  ]

  trackingGuidelines.forEach((section) => {
    yPosition = checkNewPage(30)
    
    doc.setFontSize(11)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(142, 36, 170)
    doc.text(section.category, 25, yPosition)
    yPosition += 8
    
    section.items.forEach((item) => {
      doc.setFontSize(9)
      doc.setFont('helvetica', 'normal')
      doc.setTextColor(0, 0, 0)
      yPosition = addWrappedText(`• ${item}`, 30, yPosition, pageWidth - 55, 9)
      yPosition += 3
    })
    yPosition += 8
  })

  // Journaling prompts
  yPosition = checkNewPage(50)
  addColoredBox(15, yPosition - 5, pageWidth - 30, 45, '#fafafa')
  doc.setFontSize(11)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(97, 97, 97)
  doc.text('📝 Weekly Reflection Questions:', 20, yPosition + 3)
  yPosition += 10
  
  const journalPrompts = [
    'What positive changes have I noticed this week?',
    'Which supplements am I most consistent with? Why?',
    'What challenges did I face, and how can I overcome them?',
    'How is my energy comparing to last week?',
    'What adjustments might help me succeed better next week?'
  ]
  
  doc.setFontSize(9)
  doc.setFont('helvetica', 'normal')
  journalPrompts.forEach((prompt) => {
    yPosition = addWrappedText(`• ${prompt}`, 25, yPosition, pageWidth - 50, 9)
    yPosition += 3
  })

  // Safety First Section
  yPosition = checkNewPage(120)
  yPosition = addSectionHeader('🛡️ Safety First: What You Need to Know', yPosition, '#ffebee')
  
  // Critical safety warnings
  yPosition = addWarningBox('ALWAYS consult your healthcare provider before starting any supplement regimen, especially if you take medications or have health conditions.', yPosition)
  
  const safetyCategories = [
    {
      title: 'Common Supplement Interactions to Avoid',
      items: [
        'Iron + Calcium: Take at least 2 hours apart',
        'Zinc + Copper: High zinc can deplete copper',
        'Magnesium + Antibiotics: Can reduce antibiotic absorption',
        'Vitamin K + Blood thinners: Can affect medication effectiveness',
        'High-dose B6 + Certain medications: May cause nerve issues'
      ]
    },
    {
      title: 'When to Stop and Consult a Doctor',
      items: [
        'Persistent nausea, headaches, or digestive issues',
        'Unusual fatigue or energy changes',
        'Skin rashes or allergic reactions',
        'Changes in heart rate or blood pressure',
        'Any concerning new symptoms'
      ]
    },
    {
      title: 'Quality and Safety Standards',
      items: [
        'Choose supplements with third-party testing (USP, NSF, or ConsumerLab)',
        'Look for GMP (Good Manufacturing Practice) certification',
        'Avoid supplements with proprietary blends hiding ingredient amounts',
        'Check expiration dates and proper storage requirements',
        'Research the company\'s reputation and transparency'
      ]
    }
  ]

  safetyCategories.forEach((category) => {
    yPosition = checkNewPage(40)
    
    doc.setFontSize(11)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(198, 40, 40)
    doc.text(category.title, 25, yPosition)
    yPosition += 8
    
    category.items.forEach((item) => {
      doc.setFontSize(9)
      doc.setFont('helvetica', 'normal')
      doc.setTextColor(0, 0, 0)
      yPosition = addWrappedText(`• ${item}`, 30, yPosition, pageWidth - 55, 9)
      yPosition += 3
    })
    yPosition += 10
  })

  // Important Notes and Disclaimers
  yPosition = checkNewPage(80)
  yPosition = addSectionHeader('⚖️ Important Disclaimers', yPosition, '#f5f5f5')

  const disclaimers = [
    'This report is for informational purposes only and is not intended as medical advice.',
    'Individual results may vary significantly. What works for others may not work for you.',
    'Start with lower doses and gradually increase as tolerated.',
    'Pregnant or nursing women should avoid most supplements unless prescribed.',
    'Children and elderly individuals may need different dosages - consult a healthcare provider.',
    'Some supplements can affect blood sugar, blood pressure, or blood clotting.',
    'Quality varies significantly between brands - research before purchasing.',
    'A balanced diet and healthy lifestyle remain the foundation of good health.',
    'Regular medical check-ups and lab work may be helpful when taking multiple supplements.'
  ]

  doc.setFontSize(9)
  doc.setFont('helvetica', 'normal')
  doc.setTextColor(0, 0, 0)
  disclaimers.forEach((disclaimer) => {
    yPosition = checkNewPage(12)
    yPosition = addWrappedText(`• ${disclaimer}`, 25, yPosition, pageWidth - 50, 9)
    yPosition += 5
  })

  // Quick Reference Summary Page
  doc.addPage()
  yPosition = 20
  
  // Quick reference header
  addColoredBox(10, 10, pageWidth - 20, pageHeight - 20, '#f8f9fa')
  doc.setDrawColor(0, 123, 255)
  doc.setLineWidth(2)
  doc.rect(10, 10, pageWidth - 20, pageHeight - 20)
  
  doc.setFontSize(18)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(0, 123, 255)
  doc.text('📋 Quick Reference Guide', pageWidth / 2, yPosition, { align: 'center' })
  yPosition += 20
  
  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.setTextColor(108, 117, 125)
  doc.text('Keep this page handy for daily reference', pageWidth / 2, yPosition, { align: 'center' })
  yPosition += 20

  // Your supplements summary
  if (supplements.length > 0) {
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(0, 0, 0)
    doc.text('Your Supplements', 20, yPosition)
    yPosition += 10
    
    supplements.slice(0, 5).forEach((supplement, index) => {
      doc.setFontSize(10)
      doc.setFont('helvetica', 'bold')
      doc.setTextColor(25, 118, 210)
      doc.text(`${index + 1}. ${supplement.name}`, 25, yPosition)
      yPosition += 6
      
      if (supplement.dosage) {
        doc.setFontSize(9)
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(0, 0, 0)
        doc.text(`Dosage: ${supplement.dosage}`, 30, yPosition)
        yPosition += 4
      }
      
      if (supplement.timing) {
        doc.setFontSize(9)
        doc.setFont('helvetica', 'normal')
        doc.text(`Timing: ${supplement.timing}`, 30, yPosition)
        yPosition += 4
      }
      yPosition += 5
    })
    
    if (supplements.length > 5) {
      doc.setFontSize(9)
      doc.setFont('helvetica', 'italic')
      doc.setTextColor(108, 117, 125)
      doc.text(`+ ${supplements.length - 5} more (see full report)`, 25, yPosition)
      yPosition += 10
    }
  }

  // Daily routine
  yPosition += 10
  doc.setFontSize(14)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(0, 0, 0)
  doc.text('Daily Routine Checklist', 20, yPosition)
  yPosition += 10
  
  const dailyChecklist = [
    'Take morning supplements with breakfast',
    'Rate energy/mood (1-5) in journal',
    'Eat 1-2 recommended foods',
    'Take evening supplements (if any)',
    'Note any changes or effects'
  ]
  
  dailyChecklist.forEach((item) => {
    doc.setFontSize(9)
    doc.setFont('helvetica', 'normal')
    doc.setTextColor(0, 0, 0)
    doc.text(`☐ ${item}`, 25, yPosition)
    yPosition += 5
  })

  // Emergency contacts
  yPosition += 15
  doc.setFontSize(14)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(198, 40, 40)
  doc.text('⚠️ When to Contact Your Doctor', 20, yPosition)
  yPosition += 8
  
  const emergencyWarnings = [
    'Severe nausea or digestive upset',
    'Unusual heart palpitations',
    'Persistent headaches or dizziness',
    'Skin rashes or allergic reactions'
  ]
  
  emergencyWarnings.forEach((warning) => {
    doc.setFontSize(9)
    doc.setFont('helvetica', 'normal')
    doc.setTextColor(0, 0, 0)
    doc.text(`• ${warning}`, 25, yPosition)
    yPosition += 4
  })

  // Bottom reminder
  doc.setFontSize(8)
  doc.setFont('helvetica', 'italic')
  doc.setTextColor(108, 117, 125)
  doc.text('Remember: Start with ONE supplement, monitor effects, consult healthcare providers', pageWidth / 2, pageHeight - 30, { align: 'center' })

  // Enhanced Footer for all pages
  const totalPages = doc.getNumberOfPages()
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i)
    
    // Footer background
    addColoredBox(0, pageHeight - 20, pageWidth, 20, '#f8f9fa')
    doc.setDrawColor(220, 220, 220)
    doc.setLineWidth(0.5)
    doc.line(0, pageHeight - 20, pageWidth, pageHeight - 20)
    
    // Page number
    doc.setFontSize(8)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(108, 117, 125)
    doc.text(`Page ${i} of ${totalPages}`, pageWidth - 25, pageHeight - 8)
    
    // Company info
    doc.setFont('helvetica', 'normal')
    doc.text('Generated by LifeSupplier - Your Personalized Health Companion', 15, pageHeight - 8)
    
    // Disclaimer on bottom
    if (i === totalPages) {
      doc.setFontSize(7)
      doc.setFont('helvetica', 'italic')
      doc.setTextColor(150, 150, 150)
      doc.text('This report is for informational purposes only. Consult healthcare professionals before making changes.', pageWidth / 2, pageHeight - 3, { align: 'center' })
    }
  }

  // Save the PDF
  const fileName = `LifeSupplier-Health-Report-${currentDate.replace(/\s/g, '-')}.pdf`
  doc.save(fileName)
}

// Helper function to get user-friendly answer summary
export function generateAnswerSummary(answers: Record<string, string>): string {
  const summaryParts: string[] = []
  
  if (answers['1']) summaryParts.push(`Gender: ${answers['1']}`)
  if (answers['2']) summaryParts.push(`Age: ${answers['2']}`)
  if (answers['3']) summaryParts.push(`Energy Level: ${answers['3']}`)
  if (answers['4']) summaryParts.push(`Sleep Issues: ${answers['4']}`)
  if (answers['5']) summaryParts.push(`Joint Pain: ${answers['5']}`)
  if (answers['6']) summaryParts.push(`Exercise Frequency: ${answers['6']}`)
  if (answers['7']) summaryParts.push(`Diet Type: ${answers['7']}`)
  if (answers['8']) summaryParts.push(`Regular Stress: ${answers['8']}`)
  if (answers['9']) summaryParts.push(`Digestive Health: ${answers['9']}`)
  if (answers['10']) summaryParts.push(`Regular Medications: ${answers['10']}`)

  return summaryParts.join(', ')
}