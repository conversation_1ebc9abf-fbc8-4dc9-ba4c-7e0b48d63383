import { useState, useCallback } from 'react'

export interface QuizResult {
  health_tag_name: string
  health_tag_description: string
  recommendation_type: 'supplement' | 'food'
  recommendation_id: string
  recommendation_name: string
  recommendation_details: {
    description: string
    benefits: string[]
    side_effects?: string[]
    contraindications?: string[]
    dosage_info?: {
      min_dose?: string
      max_dose?: string
      timing?: string
      form?: string
      with_food?: boolean
    }
    interactions?: string[]
    pregnancy_safe?: boolean
    breastfeeding_safe?: boolean
    rationale?: string
    all_conditions?: string[]
    condition_count?: number
    nutritional_info?: any
    serving_suggestions?: string[]
  }
  priority_score?: number
  condition_count?: number
  all_conditions?: string[]
  priority?: number
  score?: number
}

export interface HealthTag {
  name: string
  description: string
}

export interface QuizProcessingResult {
  results: QuizResult[]
  identifiedHealthTags: HealthTag[]
}

export function useQuizResultsAPI() {
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const processResults = useCallback(async (answers: Record<string, string>): Promise<QuizProcessingResult> => {
    try {
      setProcessing(true)
      setError(null)

      console.log('Processing quiz results via API:', answers)

      // Use Supabase Edge Function instead of separate Deno server
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
      if (!supabaseUrl) {
        throw new Error('Supabase URL not configured')
      }

      const response = await fetch(`${supabaseUrl}/functions/v1/process-quiz`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({ answers }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to process quiz'
        try {
          const errorData = await response.json()
          errorMessage = errorData?.error || errorData?.message || errorMessage
        } catch {
          // If JSON parsing fails, use response status text
          errorMessage = response.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      console.log('API response:', data)

      if (!data.success || !data.results) {
        throw new Error('Invalid response from server')
      }

      return {
        results: data.results as QuizResult[],
        identifiedHealthTags: data.identifiedHealthTags || []
      }
    } catch (err) {
      console.error('Error processing quiz results:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to process quiz results'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setProcessing(false)
    }
  }, [])

  return { processResults, processing, error }
}