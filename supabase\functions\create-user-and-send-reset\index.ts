import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CO}t requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body
    const { email } = await req.json()

    if (!email) {
      return new Response(
        JSON.stringify({ error: 'Email is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const siteUrl = Deno.env.get('SITE_URL') || 'https://lifesupplier.app'
    
    console.log('Using SITE_URL:', siteUrl) // Debug log
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Check if user already exists
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers()
    
    if (listError) {
      console.error('Error checking existing users:', listError)
      return new Response(
        JSON.stringify({ error: 'Failed to check existing users' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if email already exists
    const existingUser = existingUsers.users.find(user => user.email === email)
    
    if (existingUser) {
      return new Response(
        JSON.stringify({ 
          success: false,
          userExists: true,
          message: 'A user with this email already exists',
          userId: existingUser.id
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Generate a random password for initial creation
    const randomPassword = Math.random().toString(36).slice(-12) + Math.random().toString(36).slice(-12)

    // Create user with email confirmation enabled for auto sign-in
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: email,
      password: randomPassword,
      email_confirm: true, // Auto-confirm email so user can sign in immediately
      user_metadata: {
        created_via: 'quiz_completion',
        needs_password_setup: true
      }
    })

    if (authError) {
      console.error('Auth error:', authError)
      return new Response(
        JSON.stringify({ error: 'Failed to create user account' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Generate password reset link that also serves as email confirmation and auto sign-in
    // We use recovery type because it allows setting a password, which is what we need
    const { data: resetData, error: resetError } = await supabase.auth.admin.generateLink({
      type: 'recovery',
      email: email,
      options: {
        // This tells Supabase to redirect to this route after handling the recovery link
        redirectTo: `${siteUrl}/dashboard`,
        // We want this link to auto-sign in the user (important!)
        // This will create a signed-in session when the user clicks the link
        emailRedirectTo: `${siteUrl}/dashboard`
      }
    })

    if (resetError) {
      console.error('Reset link generation error:', resetError)
      return new Response(
        JSON.stringify({ error: 'Failed to generate password reset link' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Send password reset email using Resend
    const resendApiKey = 're_Dofzut3H_6mFVaqm6TsQZ2tt3VAXt3kxc'
    
    const emailHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to LifeSupplier - Set Your Password</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
                line-height: 1.6;
                color: #374151;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9fafb;
            }
            .container {
                background-color: white;
                border-radius: 12px;
                padding: 40px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e5e7eb;
            }
            .logo {
                max-width: 200px;
                height: auto;
                margin-bottom: 20px;
            }
            .welcome-text {
                font-size: 24px;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 20px;
            }
            .content {
                margin-bottom: 30px;
            }
            .cta-button {
                display: inline-block;
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                padding: 14px 28px;
                text-decoration: none;
                border-radius: 8px;
                font-weight: 600;
                font-size: 16px;
                text-align: center;
                margin: 20px 0;
                transition: transform 0.2s;
            }
            .cta-button:hover {
                transform: translateY(-1px);
            }
            .footer {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
                font-size: 14px;
                color: #6b7280;
                text-align: center;
            }
            .highlight {
                background-color: #ecfdf5;
                border: 1px solid #10b981;
                border-radius: 6px;
                padding: 16px;
                margin: 20px 0;
            }
            .security-note {
                background-color: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 6px;
                padding: 12px;
                margin: 20px 0;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <img src="https://lifesupplier.app/assets/logo_and_name_rounded.png" alt="LifeSupplier" class="logo">
                <div class="welcome-text">Welcome to LifeSupplier!</div>
            </div>
            
            <div class="content">
                <p>Hi there! 👋</p>
                
                <p>Thank you for completing your health assessment quiz! We've created your personalized LifeSupplier account and you're already signed in to view your results.</p>
                
                <div class="highlight">
                    <p><strong>🔐 Your account has been created with:</strong></p>
                    <p>Email: <strong>${email}</strong></p>
                    <p><strong>You're automatically signed in!</strong> For security, please set your own password to access your account in the future.</p>
                </div>
                
                <div style="text-align: center;">
                    <a href="${resetData.properties?.action_link}" class="cta-button">
                        🔑 Set My Password & Secure My Account
                    </a>
                </div>
                
                <div class="security-note">
                    <p><strong>🛡️ Security Note:</strong> This link will expire in 24 hours for your security. You can set your password anytime from your dashboard, or request a new password reset link if needed.</p>
                </div>
                
                <p>Once you've set your password, you'll be able to:</p>
                <ul>
                    <li>✅ View your personalized health assessment results</li>
                    <li>📊 Access your detailed supplement recommendations</li>
                    <li>📧 Receive your comprehensive health report</li>
                    <li>🔄 Update your health profile anytime</li>
                </ul>
                
                <p>If you have any questions or need assistance, our support team is here to help!</p>
                
                <p>Best regards,<br>
                <strong>Sarah Supplier</strong><br>
                The LifeSupplier Team</p>
            </div>
            
            <div class="footer">
                <p>This email was sent to ${email} because you completed a health assessment on LifeSupplier.</p>
                <p>If you didn't request this, you can safely ignore this email.</p>
                <p>&copy; 2025 LifeSupplier. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `

    const resendResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Sarah Supplier <<EMAIL>>',
        to: [email],
        subject: '🔑 Welcome to LifeSupplier - Set Your Password & View Your Results',
        html: emailHtml,
      }),
    })

    if (!resendResponse.ok) {
      const resendError = await resendResponse.text()
      console.error('Resend error:', resendError)
      return new Response(
        JSON.stringify({ error: 'Failed to send welcome email' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const emailResult = await resendResponse.json()

    const signInClient = createClient(supabaseUrl, supabaseServiceKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
        })
        const { data: signInData, error: signInError } = await signInClient.auth.signInWithPassword({
        email,
        password: randomPassword
    })

    return new Response(JSON.stringify({
        success: true,
        userExists: false,
        message: 'User created and signed in',
        userId: authData.user?.id,
        accessToken: signInData.session?.access_token,
        refreshToken: signInData.session?.refresh_token,
        resetUrl: resetData?.properties?.action_link // optional
        }), {
        status: 200,
        headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
        }    
    });

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Helper function to generate random password
function generateRandomPassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}
