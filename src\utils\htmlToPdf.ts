import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export async function generatePDFFromHTML(elementId: string, filename: string): Promise<void> {
  try {
    const element = document.getElementById(elementId)
    if (!element) {
      throw new Error(`Element with id '${elementId}' not found`)
    }

    // Configure html2canvas for better quality
    const canvas = await html2canvas(element, {
      allowTaint: true,
      useCORS: true,
      scale: 2, // Higher scale for better quality
      scrollX: 0,
      scrollY: 0,
      backgroundColor: '#ffffff',
      logging: false,
      onclone: (clonedDoc) => {
        // Add print-specific styles to the cloned document
        const printStyles = `
          @media print {
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .print\\:hidden {
              display: none !important;
            }
            .print\\:page-break-before {
              page-break-before: always !important;
            }
          }
          
          /* Optimize for PDF generation - Make content much wider */
          #comprehensive-health-report {
            max-width: 1200px !important;
            width: 1200px !important;
            margin: 0 auto !important;
            padding: 40px !important;
            background: white !important;
            box-sizing: border-box !important;
          }
          
          /* Ensure full width utilization */
          .container, .max-w-4xl, .max-w-6xl, .max-w-2xl, .max-w-xl {
            max-width: 100% !important;
            width: 100% !important;
          }
          
          /* Make cards and content sections wider */
          .grid, .grid-cols-1, .grid-cols-2 {
            grid-template-columns: 1fr !important;
          }
          
          /* Expand content areas */
          .space-y-6 > *, .space-y-4 > *, .space-y-8 > * {
            width: 100% !important;
          }
          
          /* Improve readability with larger content */
          body {
            font-size: 13px !important;
            line-height: 1.5 !important;
          }
          
          /* Make sure text content spans full width */
          p, div, .text-gray-600, .text-gray-800 {
            max-width: 100% !important;
          }
        `
        const style = clonedDoc.createElement('style')
        style.innerHTML = printStyles
        clonedDoc.head.appendChild(style)
      }
    })

    const imgData = canvas.toDataURL('image/png')
    
    // Calculate PDF dimensions with better width utilization
    const imgWidth = canvas.width
    const imgHeight = canvas.height
    
    // A4 dimensions in mm
    const pageWidth = 210
    const pageHeight = 297
    const margins = 5 // Reduced to 5mm margins for more content space
    const usableWidth = pageWidth - (margins * 2)
    const usableHeight = pageHeight - (margins * 2)
    
    // Force width to use nearly the full page width
    const targetWidth = usableWidth * 0.98 // Use 98% of available width
    const widthRatio = targetWidth / imgWidth
    const heightRatio = usableHeight / imgHeight
    
    // Prioritize width over height - use width ratio unless it makes content too tall
    const ratio = imgHeight * widthRatio > usableHeight ? heightRatio : widthRatio
    
    const pdfWidth = imgWidth * ratio
    const pdfHeight = imgHeight * ratio
    
    // Center the content horizontally
    const xOffset = margins + (usableWidth - pdfWidth) / 2

    // Create PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    
    // If content is taller than one page, create multiple pages
    let heightLeft = pdfHeight
    let position = margins // Start with top margin

    // Add first page
    pdf.addImage(imgData, 'PNG', xOffset, position, pdfWidth, pdfHeight)
    heightLeft -= pageHeight

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - pdfHeight
      pdf.addPage()
      pdf.addImage(imgData, 'PNG', xOffset, position, pdfWidth, pdfHeight)
      heightLeft -= usableHeight // Use usable height for page calculations
    }

    // Save the PDF
    pdf.save(filename)
  } catch (error) {
    console.error('Error generating PDF:', error)
    throw error
  }
}

export async function generateComprehensivePDF(elementId: string): Promise<void> {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).replace(/\s/g, '-')
  
  const filename = `LifeSupplier-Health-Report-${currentDate}.pdf`
  
  await generatePDFFromHTML(elementId, filename)
}