import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { User, Shield, Key, Info } from 'lucide-react'

export function UserDebugInfo() {
  const { user } = useAuth()
  const [userDetails, setUserDetails] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const checkUserDetails = async () => {
    if (!user) return
    
    setLoading(true)
    setError(null)
    
    try {
      // Get current session details
      const { data: session, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError) {
        throw sessionError
      }

      // Get user from auth
      const { error: authError } = await supabase.auth.getUser()
      
      if (authError) {
        throw authError
      }

      const details = {
        // Basic user info
        id: user.id,
        email: user.email,
        created_at: user.created_at,
        updated_at: user.updated_at,
        last_sign_in_at: user.last_sign_in_at,
        email_confirmed_at: user.email_confirmed_at,
        
        // Metadata
        user_metadata: user.user_metadata,
        app_metadata: user.app_metadata,
        
        // Auth method info
        identities: user.identities?.map(identity => ({
          id: identity.id,
          provider: identity.provider,
          created_at: identity.created_at,
          updated_at: identity.updated_at,
          last_sign_in_at: identity.last_sign_in_at
        })),
        
        // Session info
        session: {
          access_token: session.session?.access_token ? 'Present' : 'Missing',
          refresh_token: session.session?.refresh_token ? 'Present' : 'Missing',
          expires_at: session.session?.expires_at,
          token_type: session.session?.token_type,
          user_id: session.session?.user?.id
        },
        
        // Analysis
        analysis: {
          hasPasswordAuth: user.identities?.some(identity => identity.provider === 'email') || false,
          numberOfIdentities: user.identities?.length || 0,
          needsPasswordSetup: user.user_metadata?.needs_password_setup || false,
          createdVia: user.user_metadata?.created_via || 'unknown'
        }
      }
      
      setUserDetails(details)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <Card className="mt-4 border-orange-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-600">
            <Info className="w-5 h-5" />
            Debug Info
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">No user logged in</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mt-4 border-blue-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-600">
          <Shield className="w-5 h-5" />
          User Authentication Debug Info
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={checkUserDetails}
            disabled={loading}
            size="sm"
            variant="outline"
          >
            {loading ? 'Checking...' : 'Check Auth Details'}
          </Button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {userDetails && (
          <div className="space-y-4">
            {/* Basic Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <User className="w-4 h-4" />
                Basic Info
              </h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div><strong>ID:</strong> {userDetails.id}</div>
                <div><strong>Email:</strong> {userDetails.email}</div>
                <div><strong>Created:</strong> {new Date(userDetails.created_at).toLocaleString()}</div>
                <div><strong>Last Sign In:</strong> {userDetails.last_sign_in_at ? new Date(userDetails.last_sign_in_at).toLocaleString() : 'Never'}</div>
                <div><strong>Email Confirmed:</strong> {userDetails.email_confirmed_at ? 'Yes' : 'No'}</div>
              </div>
            </div>

            {/* Authentication Analysis */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-blue-700 mb-2 flex items-center gap-2">
                <Key className="w-4 h-4" />
                Authentication Analysis
              </h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div><strong>Has Password Auth:</strong> {userDetails.analysis.hasPasswordAuth ? 'Yes' : 'No'}</div>
                <div><strong>Number of Identities:</strong> {userDetails.analysis.numberOfIdentities}</div>
                <div><strong>Needs Password Setup:</strong> {userDetails.analysis.needsPasswordSetup ? 'Yes' : 'No'}</div>
                <div><strong>Created Via:</strong> {userDetails.analysis.createdVia}</div>
              </div>
            </div>

            {/* Identity Providers */}
            {userDetails.identities && userDetails.identities.length > 0 && (
              <div className="bg-green-50 rounded-lg p-4">
                <h4 className="font-semibold text-green-700 mb-2">Identity Providers</h4>
                {userDetails.identities.map((identity: any, index: number) => (
                  <div key={index} className="text-sm bg-white rounded p-2 mb-2">
                    <div><strong>Provider:</strong> {identity.provider}</div>
                    <div><strong>Created:</strong> {new Date(identity.created_at).toLocaleString()}</div>
                    <div><strong>Last Sign In:</strong> {identity.last_sign_in_at ? new Date(identity.last_sign_in_at).toLocaleString() : 'Never'}</div>
                  </div>
                ))}
              </div>
            )}

            {/* Session Info */}
            <div className="bg-purple-50 rounded-lg p-4">
              <h4 className="font-semibold text-purple-700 mb-2">Session Info</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div><strong>Access Token:</strong> {userDetails.session.access_token}</div>
                <div><strong>Refresh Token:</strong> {userDetails.session.refresh_token}</div>
                <div><strong>Token Type:</strong> {userDetails.session.token_type}</div>
                <div><strong>Expires At:</strong> {userDetails.session.expires_at ? new Date(userDetails.session.expires_at * 1000).toLocaleString() : 'N/A'}</div>
              </div>
            </div>

            {/* Metadata */}
            {(userDetails.user_metadata || userDetails.app_metadata) && (
              <div className="bg-yellow-50 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-700 mb-2">Metadata</h4>
                {userDetails.user_metadata && (
                  <div className="mb-2">
                    <strong>User Metadata:</strong>
                    <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(userDetails.user_metadata, null, 2)}
                    </pre>
                  </div>
                )}
                {userDetails.app_metadata && (
                  <div>
                    <strong>App Metadata:</strong>
                    <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(userDetails.app_metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
