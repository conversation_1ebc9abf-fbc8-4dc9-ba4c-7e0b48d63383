import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Check, Star, Lock, Loader2 } from 'lucide-react'
import { PaymentForm } from '@/components/payment/PaymentForm'
import { useQuizResultsAPI } from '@/hooks/useQuizResultsAPI'
import { useMarketingSummary, type MarketingSummary } from '@/hooks/useMarketingSummary'
import { useNavigate } from 'react-router-dom'
import { getQuizDataFromURL, generateURLWithQuizData } from '@/utils/quizStorage'

interface PaymentProps {
  email: string
  onPaymentComplete: () => void
  onBack?: () => void
  answers?: Record<string, any>
}

export function Payment({ email, onPaymentComplete, answers }: PaymentProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [timeLeft, setTimeLeft] = useState(15 * 60) // 15 minutes in seconds
  const [paymentError, setPaymentError] = useState<string>('')
  const [showPaymentForm, setShowPaymentForm] = useState(false)
  const [marketingSummary, setMarketingSummary] = useState<MarketingSummary | null>(null)
  const [loadingSummary, setLoadingSummary] = useState(false)
  
  // State for retrieved quiz data from URL
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>(answers || {})
  const [userEmail, setUserEmail] = useState<string>(email || '')
  
  const navigate = useNavigate()
  const { processResults } = useQuizResultsAPI()
  const { generateSummary } = useMarketingSummary()

  // Retrieve quiz data from URL if props are empty
  useEffect(() => {
    if ((!answers || Object.keys(answers).length === 0) || !email) {
      console.log('Payment: Props are empty, trying to retrieve from URL')
      const quizDataResult = getQuizDataFromURL()
      
      if (quizDataResult.success && quizDataResult.data) {
        console.log('Payment: Successfully retrieved quiz data from URL:', quizDataResult.data)
        setQuizAnswers(quizDataResult.data.answers)
        setUserEmail(quizDataResult.data.email)
      } else {
        console.warn('Payment: No quiz data found in URL, redirecting to dashboard')
        navigate('/dashboard')
        return
      }
    } else {
      setQuizAnswers(answers)
      setUserEmail(email)
    }
  }, [answers, email, navigate])

  const handlePaymentSuccess = () => {
    console.log('Payment successful!')
    onPaymentComplete()
  }

  const handlePaymentError = (error: string) => {
    console.error('Payment failed:', error)
    setPaymentError(error)
  }

  const handleSkipPayment = () => {
    // For development - skip directly to results
    console.log('Development mode: Skipping payment and going directly to results')
    console.log('Quiz answers available:', Object.keys(quizAnswers).length)

    if (!quizAnswers || Object.keys(quizAnswers).length === 0) {
      console.error('No quiz answers available, redirecting to quiz')
      navigate('/quiz')
      return
    }

    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    console.log('Generated results URL:', resultsURL)
    window.location.href = resultsURL
  }

  const handleGoToResults = () => {
    console.log('Going to results with quiz data')
    console.log('Quiz answers available:', Object.keys(quizAnswers).length)

    if (!quizAnswers || Object.keys(quizAnswers).length === 0) {
      console.error('No quiz answers available, redirecting to quiz')
      alert('Quiz data is missing. Please retake the quiz to see your results.')
      navigate('/quiz')
      return
    }

    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    console.log('Generated results URL:', resultsURL)
    window.location.href = resultsURL
  }

  // Generate marketing summary from quiz answers
  useEffect(() => {
    const generateMarketingSummary = async () => {
      if (!quizAnswers || Object.keys(quizAnswers).length === 0) return
      
      try {
        setLoadingSummary(true)
        const { results, identifiedHealthTags } = await processResults(quizAnswers)
        const summary = await generateSummary(results, identifiedHealthTags)
        setMarketingSummary(summary)
      } catch (error) {
        console.error('Error generating marketing summary:', error)
        // Continue without summary if error occurs
      } finally {
        setLoadingSummary(false)
      }
    }

    generateMarketingSummary()
  }, [quizAnswers, processResults, generateSummary])

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0))
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Logo */}
      <div className="py-6 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto px-4 pb-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Check className="w-4 h-4" />
            Analysis Complete for {userEmail}
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Unlock Your Personalized Health Report</h1>
          <p className="text-lg text-gray-600">Your results are ready! Get instant access to your premium health insights.</p>
        </div>

        {/* Marketing Preview Section */}
        {marketingSummary && (
          <div className="mb-12">
            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200">
              <CardContent className="p-6 md:p-8">
                <div className="text-center mb-6">
                  <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1 mx-auto mb-4">
                    <Check className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                    Your Results Are Ready!
                  </h2>
                  <div className="mb-4">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-1">
                      Test Results +<br />
                      Premium Health Report
                    </h3>
                    <p className="text-base md:text-lg text-gray-700">7-Day Full Access</p>
                  </div>
                  <div className="text-left max-w-md mx-auto space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                      <span className="text-base text-gray-700">
                        Found <span className="font-semibold text-purple-600">{marketingSummary.totalSupplements}</span> supplements and <span className="font-semibold text-green-600">{marketingSummary.totalFoods}</span> foods for <span className="font-semibold text-red-600">{marketingSummary.totalHealthAreas}</span> health areas
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-base text-gray-700">
                        {marketingSummary.teaserSupplement ? (
                          <>Including <span className="font-medium text-purple-700">{marketingSummary.teaserSupplement}</span> for {marketingSummary.healthAreas[0]?.name || 'targeted support'}</>
                        ) : (
                          'Personalized dosages & safety guidelines included'
                        )}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-gradient-to-r from-emerald-100 to-blue-100 rounded-lg">
                  <p className="text-center text-gray-700 font-medium">
                    🔒 <span className="text-emerald-700">Unlock your complete health profile</span> with detailed supplement information, dosages, food recommendations, and safety guidelines!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Loading state for marketing summary */}
        {loadingSummary && (
          <div className="mb-12">
            <Card className="bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200">
              <CardContent className="p-6 md:p-8 text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-500" />
                <p className="text-gray-600">Analyzing your health profile...</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Default preview when no quiz data */}
        {!marketingSummary && !loadingSummary && quizAnswers && Object.keys(quizAnswers).length === 0 && (
          <div className="mb-12">
            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200">
              <CardContent className="p-6 md:p-8 text-center">
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                  🎯 Personalized Health Recommendations Await!
                </h2>
                <p className="text-lg text-gray-700 mb-6">
                  Get access to your customized supplement and nutrition plan based on your quiz responses.
                </p>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                    <div className="text-2xl md:text-3xl font-bold text-emerald-600 mb-2">
                      ✓
                    </div>
                    <div className="text-sm md:text-base text-gray-600">
                      Health Areas<br />Analysis
                    </div>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                    <div className="text-2xl md:text-3xl font-bold text-blue-600 mb-2">
                      ✓
                    </div>
                    <div className="text-sm md:text-base text-gray-600">
                      Supplement<br />Recommendations
                    </div>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                    <div className="text-2xl md:text-3xl font-bold text-green-600 mb-2">
                      ✓
                    </div>
                    <div className="text-sm md:text-base text-gray-600">
                      Targeted<br />Foods
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Timer */}
        <div className="mb-12">
          <div className="bg-gradient-to-r from-pink-100 to-pink-200 border border-pink-300 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-base md:text-lg font-semibold text-gray-900">Results Saved for 15 Minutes</span>
              <span className="text-xl md:text-2xl font-bold text-gray-900">{formatTime(timeLeft)}</span>
            </div>
          </div>
        </div>

        {/* Security Badges */}
        <div className="flex justify-center items-center flex-wrap gap-4 md:gap-6 mb-8">
          <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border">
            <img 
              src="/assets/payment_logos/Visa_2021.svg.webp" 
              alt="Visa" 
              className="h-6 md:h-8 w-auto object-contain"
            />
          </div>
          <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border">
            <img 
              src="/assets/payment_logos/Mastercard-logo.svg.webp" 
              alt="Mastercard" 
              className="h-6 md:h-8 w-auto object-contain"
            />
          </div>
          <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border">
            <img 
              src="/assets/payment_logos/Apple_Pay_logo.svg.webp" 
              alt="Apple Pay" 
              className="h-6 md:h-8 w-auto object-contain"
            />
          </div>
          <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border">
            <img 
              src="/assets/payment_logos/Google_Pay_Logo.svg.webp" 
              alt="Google Pay" 
              className="h-6 md:h-8 w-auto object-contain"
            />
          </div>
        </div>

        {/* Main CTA Button */}
        <div className="mb-8">
          {!showPaymentForm ? (
            <div className="space-y-4">
              <Button
                onClick={handleGoToResults}
                className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
              >
                View My Results (Free Preview)
              </Button>
              <Button
                onClick={() => setShowPaymentForm(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 md:py-5 text-base md:text-lg font-semibold rounded-xl shadow-lg"
              >
                Subscribe for Full Access
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {paymentError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-700 text-sm">{paymentError}</p>
                </div>
              )}
              <PaymentForm
                email={userEmail}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentError={handlePaymentError}
                isProcessing={isProcessing}
                setIsProcessing={setIsProcessing}
                quizAnswers={quizAnswers}
              />
              <Button
                onClick={() => setShowPaymentForm(false)}
                variant="outline"
                className="w-full"
              >
                Back to Product Details
              </Button>
            </div>
          )}
        </div>

        {/* What's Included Section */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 md:p-8 mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-6 md:mb-8 text-emerald-800">WHAT'S INCLUDED</h2>
          
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Your <span className="font-bold text-emerald-600">Health Test Results</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Your personalized <span className="font-bold text-emerald-600">Premium Health Report</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Personalized 30-Day <span className="font-bold text-emerald-600">Supplement Protocol</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900"><span className="font-bold text-emerald-600">Nutrient compatibility</span> breakdown</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Personalized 30-day <span className="font-bold text-emerald-600">Wellness Challenge</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Roadmap to <span className="font-bold text-emerald-600">Optimize All Your Health Goals</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">
                After 7 days, auto-renews at $19.99 billed monthly and includes unlimited 
                access to course and challenge materials. Cancel anytime.
              </p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900"><span className="font-bold text-emerald-600">Money-Back Guarantee</span></p>
            </div>
          </div>
        </div>

        {/* Techniques Section */}
        <div className="text-center mb-12">
          <h3 className="text-xl md:text-2xl font-bold mb-6 md:mb-8 text-gray-900">Techniques Used in Results Report Covered In</h3>
          <div className="flex justify-center items-center flex-wrap gap-4 md:gap-5">
            <img 
              src="/assets/institutes_logos/Forbes_logo.svg.webp" 
              alt="Forbes" 
              className="h-4 md:h-6 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
            <img 
              src="/assets/institutes_logos/Psychology-today-logo.webp" 
              alt="Psychology Today" 
              className="h-12 md:h-14 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
            <img 
              src="/assets/institutes_logos/NewYorkTimes.svg.webp" 
              alt="The New York Times" 
              className="h-4 md:h-6 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
            <img 
              src="/assets/institutes_logos/Womens-Health-logo.webp" 
              alt="Women's Health" 
              className="h-12 md:h-14 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
            <img 
              src="/assets/institutes_logos/Healthline_logo.svg.webp" 
              alt="Healthline" 
              className="h-3 md:h-5 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
            <img 
              src="/assets/institutes_logos/MedlinePlus-logo-300x175.webp" 
              alt="MedlinePlus" 
              className="h-16 md:h-24 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
            <img 
              src="/assets/institutes_logos/World_Health_Organization_Logo.svg.webp" 
              alt="World Health Organization" 
              className="h-9 md:h-10 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
            <img 
              src="/assets/institutes_logos/BIH_Logo_at-Charite_kurz_hoch_rgb.webp" 
              alt="BIH at Charité" 
              className="h-16 md:h-20 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity"
            />
          </div>
        </div>

        {/* Your Health Results Preview */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 md:mb-8 text-gray-900">Your Health Test Results</h2>
          <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-xl p-6 md:p-8 mb-6 md:mb-8">
            <div className="w-24 h-24 md:w-32 md:h-32 mx-auto mb-6 bg-gradient-to-br from-green-200 via-blue-200 to-pink-200 rounded-full"></div>
          </div>
          
          <div className="space-y-3">
            <Button
              onClick={handleGoToResults}
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
            >
              View My Results (Free Preview)
            </Button>
            <p className="text-sm text-gray-600 text-center">
              See your health analysis and basic recommendations
            </p>
          </div>
        </div>

        {/* Your Premium Health Report */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Your Premium Health Report</h2>
          <p className="text-base md:text-lg text-gray-700 mb-6 md:mb-8">
            In-depth analysis on your Health Profile and what it means for you. Covers 12 unique 
            Health factors personalized to you.
          </p>
          
          <div className="mb-6 md:mb-8">
            <div className="bg-white rounded-lg shadow-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-green-100 to-blue-100 rounded-lg p-4 h-32 md:h-40"></div>
                <div className="bg-gradient-to-br from-pink-100 to-purple-100 rounded-lg p-4 h-32 md:h-40"></div>
              </div>
            </div>
          </div>
          
          <div className="space-y-3 mb-8 md:mb-12">
            <Button
              onClick={handleGoToResults}
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
            >
              View My Health Report (Free Preview)
            </Button>
            <p className="text-sm text-gray-600 text-center">
              Access your personalized health insights and recommendations
            </p>
          </div>
        </div>

        {/* What We'll Cover Section */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 md:p-8 mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-6 md:mb-8 text-emerald-800">WHAT WE'LL COVER IN YOUR HEALTH REPORT</h2>
          
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Your <span className="font-bold text-emerald-600">Primary</span> and <span className="font-bold text-emerald-600">Secondary</span> Health Priorities</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Your health <span className="font-bold text-emerald-600">Optimization vs Maintenance</span> styles</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Supplement <span className="font-bold text-emerald-600">compatibility</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">How Health Priorities can <span className="font-bold text-emerald-600">optimize your wellness journey</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900"><span className="font-bold text-emerald-600">Tips</span> to optimize each health priority</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900"><span className="font-bold text-emerald-600">Getting comfortable</span> with your least comfortable Health Priorities</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Your style with <span className="font-bold text-emerald-600">family and lifestyle</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Health Priority and <span className="font-bold text-emerald-600">Self-Care</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Your Health Priority in the <span className="font-bold text-emerald-600">workplace</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Health Priority and your <span className="font-bold text-emerald-600">personality</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">How your Health Priority affects your <span className="font-bold text-emerald-600">finances</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Health Priorities and <span className="font-bold text-emerald-600">nutrition</span></p>
            </div>
          </div>
        </div>

        {/* Rating */}
        <div className="text-center mb-12">
          <div className="flex justify-center items-center gap-2 mb-4 flex-wrap">
            <span className="text-xl md:text-2xl font-bold">Excellent</span>
            <span className="text-xl md:text-2xl font-bold">4.5</span>
            <div className="flex gap-1">
              {[...Array(4)].map((_, i) => (
                <Star key={i} className="w-6 h-6 md:w-8 md:h-8 fill-emerald-500 text-emerald-500" />
              ))}
              <Star className="w-6 h-6 md:w-8 md:h-8 fill-gray-300 text-gray-300" />
            </div>
            <span className="text-xl md:text-2xl font-bold">464 reviews</span>
          </div>
        </div>

        {/* How You'll Benefit Section */}
        <div className="mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-6 md:mb-8 text-emerald-800">HOW YOU'LL BENEFIT</h2>
          
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Discover you & your partners full Health Priorities</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900"><span className="font-bold">Uncover compatibility areas and issues</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Learn how to <span className="font-bold">express care</span> to each other</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Resolve potential health <span className="font-bold">blockers</span> before they arise</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Learn how to unlock the fullest potential of a <span className="font-bold">deep bonded loving relationship</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Build a loving, lasting, <span className="font-bold">fulfilling relationship</span></p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900"><span className="font-bold">Unlock your fullest potential</span></p>
            </div>
          </div>
        </div>

        {/* Learn How To Section */}
        <div className="mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-6 md:mb-8 text-emerald-800">LEARN HOW TO</h2>
          
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Discover your secondary Health Priority.</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Discover your partners Health Priority.</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Uncover compatibility areas and <span className="font-bold">issues</span>.</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Learn how to best love your partner.</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Resolve Potential love blockers before they arise.</p>
            </div>
            
            <div className="flex items-start gap-4">
              <Check className="w-5 h-5 md:w-6 md:h-6 text-emerald-600 mt-1 flex-shrink-0" />
              <p className="text-base md:text-lg text-gray-900">Unlock the fullest potential of a deep bonded loving relationship.</p>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center mb-8">
          <Button
            onClick={handleGoToResults}
            className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg mb-6"
          >
            Access My Results & Health Report
          </Button>
        </div>

        {/* Development Skip Button */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-center mb-12">
            <Button
              onClick={handleSkipPayment}
              variant="outline"
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 border-gray-300"
            >
              Skip Payment (Development Only)
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}