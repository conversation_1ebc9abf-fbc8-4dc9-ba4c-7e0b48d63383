#!/usr/bin/env tsx

import { QuizTester, quickTestAllScenarios, quickTestScenario } from '../src/test-utils/quizTester'
import { QUIZ_SCENARIOS, getScenarioNames } from '../src/test-data/predefinedQuizScenarios'

interface CliOptions {
  scenario?: string
  all?: boolean
  summary?: boolean
  detailed?: boolean
  help?: boolean
  list?: boolean
  compare?: boolean
  performance?: boolean
}

function parseArgs(): CliOptions {
  const args = process.argv.slice(2)
  const options: CliOptions = {}
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--scenario':
      case '-s':
        options.scenario = args[++i]
        break
      case '--all':
      case '-a':
        options.all = true
        break
      case '--summary':
        options.summary = true
        break
      case '--detailed':
      case '-d':
        options.detailed = true
        break
      case '--list':
      case '-l':
        options.list = true
        break
      case '--compare':
      case '-c':
        options.compare = true
        break
      case '--performance':
      case '-p':
        options.performance = true
        break
      case '--help':
      case '-h':
        options.help = true
        break
    }
  }
  
  return options
}

function showHelp(): void {
  console.log(`
Quiz Scenario Testing CLI

USAGE:
  npm run test:quiz [OPTIONS]

OPTIONS:
  -s, --scenario <name>    Test a specific scenario by name
  -a, --all               Test all scenarios
  -l, --list              List all available scenarios
  -d, --detailed          Show detailed reports (default with single scenario)
      --summary           Show summary report only
  -c, --compare           Show comparison of expected vs actual recommendations
  -p, --performance       Show performance metrics
  -h, --help              Show this help message

EXAMPLES:
  npm run test:quiz --all                    # Test all scenarios with summary
  npm run test:quiz -s "Low Energy Person"   # Test specific scenario with details
  npm run test:quiz --list                   # List all available scenarios
  npm run test:quiz -a -d                    # Test all with detailed reports
  npm run test:quiz -s "Healthy Person" -c   # Test with recommendation comparison

AVAILABLE SCENARIOS:`)
  
  getScenarioNames().forEach((name, index) => {
    console.log(`  ${index + 1}. ${name}`)
  })
  
  console.log('')
}

function listScenarios(): void {
  console.log('\nAvailable Quiz Scenarios:')
  console.log('=' .repeat(50))
  
  QUIZ_SCENARIOS.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.name}`)
    console.log(`   ${scenario.description}`)
    console.log(`   Expected conditions: ${scenario.expectedConditions.join(', ')}`)
    console.log('')
  })
}

function testSpecificScenario(scenarioName: string, options: CliOptions): void {
  try {
    const scenario = QUIZ_SCENARIOS.find(s => s.name === scenarioName)
    if (!scenario) {
      console.error(`❌ Scenario "${scenarioName}" not found.`)
      console.log('\nAvailable scenarios:')
      getScenarioNames().forEach(name => console.log(`  - ${name}`))
      process.exit(1)
    }
    
    console.log(`\n🧪 Testing scenario: ${scenarioName}`)
    console.log('=' .repeat(60))
    
    const result = QuizTester.testScenario(scenario)
    
    if (options.performance) {
      console.log(`\n⚡ Performance Metrics:`)
      console.log(`   Execution Time: ${result.executionTime.toFixed(2)}ms`)
      console.log(`   Recommendations Generated: ${result.totalRecommendations}`)
      console.log(`   Conditions Identified: ${result.conditionsFound.length}`)
    }
    
    if (options.compare) {
      console.log(`\n🔍 Recommendation Comparison:`)
      const comparison = QuizTester.compareRecommendations(scenario, result.results)
      console.log(`   ✅ Expected matches: ${comparison.matches.join(', ') || 'None'}`)
      console.log(`   ❌ Missing expected: ${comparison.missing.join(', ') || 'None'}`)
      console.log(`   ➕ Additional found: ${comparison.extra.join(', ') || 'None'}`)
    }
    
    if (options.detailed !== false) {
      const report = QuizTester.generateDetailedReport(result)
      console.log(report)
    }
    
    if (result.conditionsMatched && result.totalRecommendations > 0) {
      console.log('✅ Test PASSED')
    } else {
      console.log('❌ Test FAILED')
      process.exit(1)
    }
    
  } catch (error) {
    console.error(`❌ Error testing scenario "${scenarioName}":`, error)
    process.exit(1)
  }
}

function testAllScenarios(options: CliOptions): void {
  try {
    console.log('\n🧪 Testing all quiz scenarios...')
    console.log('=' .repeat(80))
    
    const summary = QuizTester.testAllScenarios()
    
    if (options.performance) {
      console.log(`\n⚡ Overall Performance:`)
      console.log(`   Total Execution Time: ${summary.totalExecutionTime.toFixed(2)}ms`)
      console.log(`   Average per Scenario: ${summary.averageExecutionTime.toFixed(2)}ms`)
      
      // Find fastest and slowest scenarios
      const sortedByTime = summary.results.sort((a, b) => a.executionTime - b.executionTime)
      console.log(`   Fastest: ${sortedByTime[0].scenario.name} (${sortedByTime[0].executionTime.toFixed(2)}ms)`)
      console.log(`   Slowest: ${sortedByTime[sortedByTime.length - 1].scenario.name} (${sortedByTime[sortedByTime.length - 1].executionTime.toFixed(2)}ms)`)
    }
    
    const report = QuizTester.generateSummaryReport(summary)
    console.log(report)
    
    if (options.detailed) {
      console.log('\n📋 Detailed Results:')
      console.log('=' .repeat(80))
      
      summary.results.forEach(result => {
        const detailedReport = QuizTester.generateDetailedReport(result)
        console.log(detailedReport)
      })
    }
    
    if (options.compare) {
      console.log('\n🔍 Recommendation Comparisons:')
      console.log('=' .repeat(50))
      
      summary.results.forEach(result => {
        if (result.scenario.expectedSupplementTypes) {
          console.log(`\n${result.scenario.name}:`)
          const comparison = QuizTester.compareRecommendations(result.scenario, result.results)
          console.log(`  ✅ Matches: ${comparison.matches.join(', ') || 'None'}`)
          console.log(`  ❌ Missing: ${comparison.missing.join(', ') || 'None'}`)
          console.log(`  ➕ Extra: ${comparison.extra.join(', ') || 'None'}`)
        }
      })
    }
    
    console.log(`\n${summary.successfulTests === summary.totalScenarios ? '✅' : '❌'} Overall: ${summary.successfulTests}/${summary.totalScenarios} tests passed`)
    
    if (summary.failedTests > 0) {
      console.log('\n❌ Failed scenarios:')
      summary.results
        .filter(r => !r.conditionsMatched || r.totalRecommendations === 0)
        .forEach(r => {
          console.log(`   - ${r.scenario.name}`)
          if (r.conditionsMissed.length > 0) {
            console.log(`     Missing conditions: ${r.conditionsMissed.join(', ')}`)
          }
        })
      process.exit(1)
    }
    
  } catch (error) {
    console.error('❌ Error running quiz tests:', error)
    process.exit(1)
  }
}

function main(): void {
  const options = parseArgs()
  
  if (options.help) {
    showHelp()
    return
  }
  
  if (options.list) {
    listScenarios()
    return
  }
  
  if (options.scenario) {
    testSpecificScenario(options.scenario, options)
  } else if (options.all) {
    testAllScenarios(options)
  } else {
    console.log('❌ Please specify either --scenario <name> or --all')
    console.log('Use --help for more information')
    process.exit(1)
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Run the CLI
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}