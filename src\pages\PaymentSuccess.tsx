import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Check, ArrowRight, Loader2, AlertCircle } from 'lucide-react'
import { usePaymentVerification } from '@/hooks/usePaymentVerification'
import { useAuth } from '@/contexts/AuthContext'
import { getQuizDataFromURL } from '@/utils/quizStorage'

interface PaymentSuccessProps {
  onContinue: (quizAnswers?: Record<string, any>, userEmail?: string) => void
}

export function PaymentSuccess({ onContinue }: PaymentSuccessProps) {
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>({})
  const [userEmail, setUserEmail] = useState<string>('')
  const { user } = useAuth()
  const { verifyPayment } = usePaymentVerification()
  
  const MAX_RETRIES = 10
  const RETRY_DELAY = 3000 // 3 seconds to allow webhook processing

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const sessionIdParam = urlParams.get('session_id')
    setSessionId(sessionIdParam)
    
    // Attempt to recover quiz data from URL
    console.log('PaymentSuccess: Attempting to recover quiz data from URL')
    const quizDataResult = getQuizDataFromURL()
    
    if (quizDataResult.success && quizDataResult.data) {
      console.log('PaymentSuccess: Successfully recovered quiz data:', quizDataResult.data)
      setQuizAnswers(quizDataResult.data.answers)
      setUserEmail(quizDataResult.data.email)
    } else {
      console.warn('PaymentSuccess: No quiz data found in URL')
      // If user is logged in, use their email as fallback
      if (user?.email) {
        setUserEmail(user.email)
      }
    }
    
    // Start verification process
    if (sessionIdParam) {
      verifyPaymentAndSubscription(sessionIdParam)
    } else {
      // No session ID - this might be a direct access, assume success for now
      setVerificationStatus('success')
    }
  }, [user])
  
  const verifyPaymentAndSubscription = async (sessionId: string) => {
    try {
      console.log('Verifying payment for session:', sessionId)
      await pollForPaymentVerification(sessionId)
    } catch (error) {
      console.error('Payment verification error:', error)
      setError(error instanceof Error ? error.message : 'Payment verification failed')
      setVerificationStatus('error')
    }
  }
  
  const pollForPaymentVerification = async (sessionId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const poll = async (attempt: number) => {
        try {
          console.log(`Payment verification attempt ${attempt} of ${MAX_RETRIES} for session: ${sessionId}`)
          
          // Use the edge function to verify payment status
          const result = await verifyPayment(sessionId, user?.email)
          
          if (result.success && result.subscriptionActive) {
            console.log('✅ Payment and subscription verified successfully!')
            setVerificationStatus('success')
            resolve()
            return
          }
          
          if (!result.success) {
            console.log(`Attempt ${attempt}: Edge function error:`, result.error)
          } else if (!result.subscriptionActive) {
            console.log(`Attempt ${attempt}: Subscription not active yet, retrying...`)
          }
          
          setRetryCount(attempt)
          
          if (attempt >= MAX_RETRIES) {
            reject(new Error(
              result.error || 
              'Payment verification timed out. Your payment was processed but account setup is taking longer than expected. Please contact support if this continues.'
            ))
            return
          }
          
          setTimeout(() => poll(attempt + 1), RETRY_DELAY)
          
        } catch (error) {
          console.error(`Payment verification attempt ${attempt} failed:`, error)
          if (attempt >= MAX_RETRIES) {
            reject(error)
            return
          }
          
          setTimeout(() => poll(attempt + 1), RETRY_DELAY)
        }
      }
      
      poll(1)
    })
  }
  
  const handleRetry = () => {
    if (sessionId) {
      setVerificationStatus('loading')
      setError(null)
      setRetryCount(0)
      verifyPaymentAndSubscription(sessionId)
    }
  }
  
  const handleContinueAnyway = () => {
    // Continue even if verification failed, but pass recovered quiz data
    console.log('PaymentSuccess: Continuing anyway with quiz data:', { quizAnswers, userEmail })
    onContinue(quizAnswers, userEmail)
  }

  // Show loading state while verifying payment
  if (verificationStatus === 'loading') {
    return (
      <div className="min-h-screen bg-white">
        {/* Logo */}
        <div className="py-6 px-4">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-sm"></div>
              </div>
              <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
            </div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto px-4 pb-8">
          <div className="text-center mb-12">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Loader2 className="w-10 h-10 text-blue-600 animate-spin" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Processing Your Payment</h1>
            <p className="text-lg text-gray-600 mb-4">
              Please wait while we verify your payment and set up your account...
            </p>
            {retryCount > 0 && (
              <p className="text-sm text-gray-500">
                Verification attempt {retryCount} of {MAX_RETRIES}
              </p>
            )}
          </div>
          
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center gap-2 text-blue-600">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm font-medium">
                    {retryCount <= 1 ? 'Verifying payment with Stripe...' : 
                     retryCount <= 3 ? 'Setting up your account...' : 
                     'Finalizing subscription details...'}
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  {retryCount <= 3 ? 'This usually takes just a few seconds' : 
                   'Almost done - just a few more moments'}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }
  
  // Show error state if verification failed
  if (verificationStatus === 'error') {
    return (
      <div className="min-h-screen bg-white">
        {/* Logo */}
        <div className="py-6 px-4">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-sm"></div>
              </div>
              <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
            </div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto px-4 pb-8">
          <div className="text-center mb-12">
            <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertCircle className="w-10 h-10 text-yellow-600" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Payment Processing</h1>
            <p className="text-lg text-gray-600 mb-4">
              Your payment was received, but we're still setting up your account.
            </p>
          </div>
          
          <Card className="mb-8 border-yellow-200 bg-yellow-50">
            <CardContent className="p-6">
              <div className="text-center space-y-3">
                <div className="text-yellow-800 font-medium">What's happening?</div>
                <div className="text-sm text-yellow-700">
                  {error || 'We\'re processing your payment and setting up your subscription. This usually takes less than a minute, but can occasionally take up to 3-5 minutes during high traffic.'}
                </div>
                {retryCount > 5 && (
                  <div className="text-xs text-yellow-600 mt-2 p-2 bg-yellow-100 rounded">
                    Taking longer than usual? Your payment was received - we're just waiting for all systems to sync up. 
                    You can continue anyway and check back later, or contact support if this persists.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          <div className="space-y-4">
            <Button
              onClick={handleRetry}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 text-lg font-bold rounded-xl"
            >
              <Loader2 className="w-4 h-4 mr-2" />
              Check Again
            </Button>
            
            <Button
              onClick={handleContinueAnyway}
              variant="outline"
              className="w-full border-gray-300 text-gray-700 py-4 text-lg rounded-xl"
            >
              Continue Anyway
            </Button>
          </div>
          
          <div className="mt-8 text-center text-sm text-gray-600">
            <p>
              If you continue to have issues, please contact support with session ID: 
              <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                {sessionId?.slice(-8) || 'N/A'}
              </span>
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Show success state
  return (
    <div className="min-h-screen bg-white">
      {/* Logo */}
      <div className="py-6 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto px-4 pb-8">
        {/* Success Message */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Check className="w-10 h-10 text-emerald-600" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Payment Successful!</h1>
          <p className="text-lg text-gray-600">
            Thank you for your purchase. Your health report is now ready to view.
          </p>
        </div>

        {/* Purchase Details */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Purchase Details</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Premium Health Report</span>
                <span>$9.97</span>
              </div>
              <div className="flex justify-between">
                <span>7-Day Full Access</span>
                <span>Included</span>
              </div>
              {sessionId && (
                <div className="flex justify-between text-xs pt-2 border-t">
                  <span>Session ID:</span>
                  <span className="font-mono">{sessionId.slice(-8)}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <Button
          onClick={() => {
            console.log('PaymentSuccess: Success - continuing with quiz data:', { quizAnswers, userEmail })
            onContinue(quizAnswers, userEmail)
          }}
          className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
        >
          View My Health Report
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>

        {/* Additional Info */}
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>
            You'll receive an email confirmation shortly. Your subscription will auto-renew 
            at $9.99 every 4 weeks after the 7-day trial. Cancel anytime.
          </p>
        </div>
      </div>
    </div>
  )
}