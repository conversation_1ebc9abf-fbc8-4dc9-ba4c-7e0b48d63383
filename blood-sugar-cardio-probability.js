// Analysis: Supplement Selection Probability for Low Blood Sugar + Cardiovascular Problems
// Based on weighted random selection from weighted-random-examples.js

// Supplements that match your conditions (Low Energy & Blood Sugar + Cardiovascular Health Support)
const AVAILABLE_SUPPLEMENTS = [
  // MULTI-CONDITION SUPPLEMENTS (cover both your conditions)
  { 
    name: "Ceylon Cinnamon", 
    score: 140, 
    conditions: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"],
    type: "MULTI-CONDITION" 
  },
  
  // SINGLE-CONDITION SUPPLEMENTS (Low Energy & Blood Sugar only)
  { 
    name: "B-Complex Vitamins", 
    score: 140, 
    conditions: ["Low Energy & Blood Sugar", "Brain Fog & Focus"],
    type: "BLOOD_SUGAR_ONLY" 
  },
  { 
    name: "Chromium Picolinate", 
    score: 140, 
    conditions: ["Low Energy & Blood Sugar", "Weight Management Support"],
    type: "BLOOD_SUGAR_ONLY" 
  },
  { 
    name: "Rhodiola Rosea", 
    score: 120, 
    conditions: ["Physical Performance", "Low Energy & Blood Sugar"],
    type: "BLOOD_SUGAR_ONLY" 
  },
  { 
    name: "Biotin", 
    score: 120, 
    conditions: ["Hair & Skin Support", "Low Energy & Blood Sugar"],
    type: "BLOOD_SUGAR_ONLY" 
  },
  
  // SINGLE-CONDITION SUPPLEMENTS (Cardiovascular only)
  { 
    name: "Hawthorn Berry", 
    score: 100, 
    conditions: ["Cardiovascular Health Support", "Digestive Health Support"],
    type: "CARDIO_ONLY" 
  },
  { 
    name: "Green Tea Extract", 
    score: 100, 
    conditions: ["Weight Management Support", "Cardiovascular Health Support"],
    type: "CARDIO_ONLY" 
  }
];

// Weighted Random Selection Function (from weighted-random-examples.js)
function weightedRandomSelection(supplements) {
  if (!supplements || supplements.length === 0) return null;
  if (supplements.length === 1) return supplements[0];
  
  const totalWeight = supplements.reduce((sum, item) => sum + item.score, 0);
  let random = Math.random() * totalWeight;
  
  for (const item of supplements) {
    random -= item.score;
    if (random <= 0) return item;
  }
  return supplements[supplements.length - 1];
}

// Calculate probabilities
const totalWeight = AVAILABLE_SUPPLEMENTS.reduce((sum, s) => sum + s.score, 0);

console.log("🩸❤️ SUPPLEMENT PROBABILITY ANALYSIS");
console.log("Conditions: Low Blood Sugar + Cardiovascular Problems");
console.log("=".repeat(70));

console.log("\n📊 AVAILABLE SUPPLEMENTS & THEIR SELECTION CHANCES:");
console.log("-".repeat(70));

AVAILABLE_SUPPLEMENTS
  .sort((a, b) => b.score - a.score)
  .forEach(supplement => {
    const probability = (supplement.score / totalWeight * 100).toFixed(1);
    const typeIcon = supplement.type === "MULTI-CONDITION" ? "⭐" : 
                    supplement.type === "BLOOD_SUGAR_ONLY" ? "🩸" : "❤️";
    
    console.log(`${typeIcon} ${supplement.name.padEnd(20)} | Score: ${supplement.score.toString().padEnd(3)} | Chance: ${probability.padEnd(5)}%`);
    console.log(`   Covers: ${supplement.conditions.join(" + ")}`);
    console.log("");
  });

console.log("🎯 PROBABILITY RANKING (Highest to Lowest Chance):");
console.log("-".repeat(50));

const probabilityRanking = AVAILABLE_SUPPLEMENTS
  .map(s => ({
    name: s.name,
    probability: (s.score / totalWeight * 100).toFixed(1),
    score: s.score,
    type: s.type
  }))
  .sort((a, b) => b.probability - a.probability);

probabilityRanking.forEach((item, index) => {
  const medal = index === 0 ? "🥇" : index === 1 ? "🥈" : index === 2 ? "🥉" : `${index + 1}.`;
  const typeIcon = item.type === "MULTI-CONDITION" ? "⭐" : 
                  item.type === "BLOOD_SUGAR_ONLY" ? "🩸" : "❤️";
  console.log(`${medal} ${item.name} - ${item.probability}% chance ${typeIcon}`);
});

// Simulation to prove the probabilities
console.log("\n🧪 SIMULATION RESULTS (10,000 selections):");
console.log("-".repeat(50));

const simulationResults = {};
const simulations = 10000;

for (let i = 0; i < simulations; i++) {
  const selected = weightedRandomSelection(AVAILABLE_SUPPLEMENTS);
  simulationResults[selected.name] = (simulationResults[selected.name] || 0) + 1;
}

Object.entries(simulationResults)
  .sort((a, b) => b[1] - a[1])
  .forEach(([name, count]) => {
    const actualPercent = (count / simulations * 100).toFixed(1);
    const supplement = AVAILABLE_SUPPLEMENTS.find(s => s.name === name);
    const expectedPercent = (supplement.score / totalWeight * 100).toFixed(1);
    const deviation = Math.abs(actualPercent - expectedPercent).toFixed(1);
    
    console.log(`${name.padEnd(20)} | ${count.toString().padEnd(4)} times | ${actualPercent.padEnd(5)}% | Expected: ${expectedPercent}% | ±${deviation}%`);
  });

console.log("\n" + "=".repeat(70));
console.log("🎯 KEY INSIGHTS FOR YOUR CONDITIONS:");
console.log("=".repeat(70));

const topThree = probabilityRanking.slice(0, 3);
console.log("🥇 HIGHEST PROBABILITY SUPPLEMENTS:");
topThree.forEach(item => {
  console.log(`   • ${item.name} (${item.probability}% chance)`);
});

const multiCondition = AVAILABLE_SUPPLEMENTS.filter(s => s.type === "MULTI-CONDITION");
if (multiCondition.length > 0) {
  console.log(`\n⭐ MULTI-CONDITION ADVANTAGE:`);
  console.log(`   • ${multiCondition[0].name} covers BOTH your conditions`);
  console.log(`   • ${multiCondition[0].score} score gives it ${(multiCondition[0].score / totalWeight * 100).toFixed(1)}% selection chance`);
  console.log(`   • Most efficient choice for your specific health profile`);
}

console.log(`\n📈 SCORE DISTRIBUTION:`);
console.log(`   • High Score (140): ${AVAILABLE_SUPPLEMENTS.filter(s => s.score === 140).length} supplements`);
console.log(`   • Medium Score (120): ${AVAILABLE_SUPPLEMENTS.filter(s => s.score === 120).length} supplements`);
console.log(`   • Lower Score (100): ${AVAILABLE_SUPPLEMENTS.filter(s => s.score === 100).length} supplements`);

console.log(`\n🎲 VARIETY BENEFIT:`);
console.log(`   • Instead of always getting the same supplement`);
console.log(`   • You'll get variety while favoring the best options`);
console.log(`   • Higher scores = Higher probability (but not 100% certainty)`);
