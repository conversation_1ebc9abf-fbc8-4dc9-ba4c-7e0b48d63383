import { Application, Router } from "@oak/oak";
import { load } from "@std/dotenv";
import { processQuizAnswers } from "./quiz.ts";
import <PERSON><PERSON> from "stripe";

// Load environment variables
await load({ export: true });

// Initialize Stripe (will be undefined if no key provided)
const stripe = Deno.env.get("STRIPE_SECRET_KEY") 
  ? new Stripe(Deno.env.get("STRIPE_SECRET_KEY")!, {
      apiVersion: "2024-12-18.acacia",
    })
  : null;

const app = new Application();
const router = new Router();

// CORS middleware
app.use(async (ctx, next) => {
  ctx.response.headers.set("Access-Control-Allow-Origin", "http://localhost:5173");
  ctx.response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  ctx.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (ctx.request.method === "OPTIONS") {
    ctx.response.status = 200;
    return;
  }
  
  await next();
});

// Logging middleware
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  console.log(`${ctx.request.method} ${ctx.request.url} - ${ms}ms`);
});

// Health check endpoint
router.get("/health", (ctx) => {
  ctx.response.body = { status: "ok", timestamp: new Date().toISOString() };
});

// Quiz processing endpoint
router.post("/api/quiz/process", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const { answers } = body;
    
    if (!answers || typeof answers !== 'object') {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing or invalid answers" };
      return;
    }

    console.log('Processing quiz answers:', answers);
    const results = processQuizAnswers(answers);
    
    ctx.response.body = { 
      success: true,
      results,
      count: results.length
    };
  } catch (error) {
    console.error('Quiz processing error:', error);
    ctx.response.status = 500;
    ctx.response.body = { error: "Failed to process quiz answers" };
  }
});

// Payment endpoints
router.post("/api/payments/create-intent", async (ctx) => {
  try {
    if (!stripe) {
      ctx.response.status = 503;
      ctx.response.body = { error: "Stripe not configured" };
      return;
    }

    const body = await ctx.request.body.json();
    const { amount = 997, currency = 'usd', metadata = {} } = body; // Default $9.97 for health report

    console.log('Creating payment intent:', { amount, currency, metadata });

    const paymentIntent = await stripe.paymentIntents.create({
      amount, // Amount in cents
      currency,
      metadata: {
        product: 'health_report',
        ...metadata,
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    ctx.response.body = {
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id,
    };
  } catch (error) {
    console.error('Payment intent creation error:', error);
    ctx.response.status = 500;
    ctx.response.body = { error: "Failed to create payment intent" };
  }
});

// Payment success handler
async function handlePaymentSuccess(paymentIntent: any) {
  try {
    console.log('Processing successful payment:', paymentIntent.id);
    console.log('Payment metadata:', paymentIntent.metadata);
    
    // Here you would typically:
    // 1. Create or update user subscription in database
    // 2. Grant access to premium features
    // 3. Send confirmation email
    // 4. Generate and email the health report
    
    // For now, we'll just log the success
    console.log(`✅ Payment processed successfully for amount: $${paymentIntent.amount / 100}`);
    
    // In a real implementation, you might:
    // - Insert into subscriptions table with Supabase
    // - Queue a background job to generate the health report
    // - Send an email with the report attachment
    
  } catch (error) {
    console.error('Error handling payment success:', error);
  }
}

// Payment failure handler
async function handlePaymentFailure(paymentIntent: any) {
  try {
    console.log('Processing failed payment:', paymentIntent.id);
    console.log('Failure reason:', paymentIntent.last_payment_error?.message);
    
    // Here you would typically:
    // 1. Log the failure for analytics
    // 2. Send failure notification email if needed
    // 3. Update any retry mechanisms
    
    console.log(`❌ Payment failed for amount: $${paymentIntent.amount / 100}`);
    
  } catch (error) {
    console.error('Error handling payment failure:', error);
  }
}

router.post("/api/webhooks/stripe", async (ctx) => {
  try {
    if (!stripe) {
      ctx.response.status = 503;
      ctx.response.body = { error: "Stripe not configured" };
      return;
    }

    const body = await ctx.request.body.text();
    const signature = ctx.request.headers.get("stripe-signature");
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");

    if (!signature || !webhookSecret) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing signature or webhook secret" };
      return;
    }

    let event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      ctx.response.status = 400;
      ctx.response.body = { error: "Invalid signature" };
      return;
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        console.log('Payment succeeded:', paymentIntent.id);
        await handlePaymentSuccess(paymentIntent);
        break;
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object;
        console.log('Payment failed:', failedPayment.id);
        await handlePaymentFailure(failedPayment);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    ctx.response.body = { received: true };
  } catch (error) {
    console.error('Webhook error:', error);
    ctx.response.status = 500;
    ctx.response.body = { error: "Webhook processing failed" };
  }
});

app.use(router.routes());
app.use(router.allowedMethods());

const PORT = 8000;
console.log(`🚀 Server starting on http://localhost:${PORT}`);
await app.listen({ port: PORT });