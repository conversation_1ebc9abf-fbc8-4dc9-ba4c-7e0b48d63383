import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { AuthForm } from '@/components/auth/AuthForm'
import { User, LogOut, ChevronDown } from 'lucide-react'

export function AuthButtons() {
  const { user, signOut, loading } = useAuth()
  const navigate = useNavigate()
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login')
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleAuthSuccess = () => {
    setShowAuthModal(false)
    // User will be redirected by SmartRedirect logic based on subscription status
    navigate('/')
  }

  const handleSignOut = async () => {
    await signOut()
    setShowDropdown(false)
    navigate('/')
  }

  const handleDashboard = () => {
    setShowDropdown(false)
    navigate('/dashboard')
  }

  const openLogin = () => {
    setAuthMode('login')
    setShowAuthModal(true)
  }

  const openSignup = () => {
    setAuthMode('signup')
    setShowAuthModal(true)
  }

  if (loading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
    )
  }

  if (user) {
    return (
      <div className="relative" ref={dropdownRef}>
        <Button 
          variant="outline" 
          onClick={() => setShowDropdown(!showDropdown)}
          className="flex items-center space-x-2 bg-white hover:bg-gray-50 border-gray-200"
        >
          <User className="h-4 w-4" />
          <span className="hidden sm:inline text-sm">
            {user.user_metadata?.full_name || user.email?.split('@')[0] || 'Account'}
          </span>
          <ChevronDown className="h-4 w-4" />
        </Button>
        
        {showDropdown && (
          <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            <div className="px-4 py-3 border-b border-gray-100">
              <p className="text-sm font-medium text-gray-900">
                {user.user_metadata?.full_name || 'User'}
              </p>
              <p className="text-xs text-gray-500">{user.email}</p>
            </div>
            <div className="py-1">
              <button
                onClick={handleDashboard}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <User className="mr-3 h-4 w-4" />
                Dashboard
              </button>
              <button
                onClick={handleSignOut}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <LogOut className="mr-3 h-4 w-4" />
                Sign Out
              </button>
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      <div className="flex items-center space-x-3">
        <Button 
          variant="ghost" 
          onClick={openLogin}
          className="text-gray-700 hover:text-gray-900 hover:bg-gray-100"
        >
          Login
        </Button>
        <Button 
          onClick={openSignup}
          className="bg-emerald-600 hover:bg-emerald-700 text-white"
        >
          Sign Up
        </Button>
      </div>

      {showAuthModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-md">
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-lg font-semibold">
                {authMode === 'login' ? 'Welcome Back' : 'Create Account'}
              </h2>
              <button
                onClick={() => setShowAuthModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            <div className="p-4">
              <AuthForm 
                mode={authMode}
                onToggleMode={() => setAuthMode(authMode === 'login' ? 'signup' : 'login')}
                onSuccess={handleAuthSuccess}
              />
            </div>
          </div>
        </div>
      )}
    </>
  )
}