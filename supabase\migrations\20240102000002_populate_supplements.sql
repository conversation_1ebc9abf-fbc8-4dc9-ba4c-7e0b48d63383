-- ============================================================================
-- Populate Supplements Data from CSV
-- ============================================================================

-- Insert comprehensive supplement data
INSERT INTO supplements (name, description, benefits, side_effects, contraindications, dosage_info, pregnancy_safe, breastfeeding_safe) VALUES

-- Multivitamin
('Multivitamin', 
'Comprehensive nutritional foundation providing essential vitamins and minerals',
ARRAY['Fills nutritional gaps', 'Supports overall health', 'Immune function', 'Energy metabolism', 'Supports stress response', 'Brain and cognitive support'],
ARRAY['May cause stomach upset if taken on empty stomach', 'Iron may cause constipation', 'B vitamins may turn urine bright yellow'],
ARRAY['Avoid if taking specific medications without consulting doctor', 'Some ingredients may interact with blood thinners'],
'{"min_dose": "1 tablet daily", "max_dose": "1 tablet daily", "timing": "With breakfast", "form": "tablet/capsule", "with_food": true}'::jsonb,
true, true),

-- Vitamin D
('Vitamin D3', 
'Essential for bone health, immune function, and mood regulation',
ARRAY['Bone health', 'Immune support', 'Mood regulation', 'Muscle function', 'Supports calcium absorption'],
ARRAY['High doses may cause nausea', 'Can increase calcium levels excessively', 'May cause kidney stones with very high doses'],
ARRAY['Avoid high doses if have kidney disease', 'Monitor levels if taking digoxin'],
'{"min_dose": "1000 IU daily", "max_dose": "4000 IU daily", "timing": "With a meal containing fat", "form": "soft gel", "with_food": true}'::jsonb,
true, true),

-- Vitamin C
('Vitamin C', 
'Powerful antioxidant supporting immune function and collagen synthesis',
ARRAY['Antioxidant protection', 'Immune support', 'Collagen synthesis', 'Iron absorption', 'Wound healing'],
ARRAY['High doses may cause digestive upset', 'May cause diarrhea above 2000mg', 'Can increase iron absorption potentially causing overload'],
ARRAY['Reduce dose if prone to kidney stones', 'May interact with certain chemotherapy drugs'],
'{"min_dose": "500mg daily", "max_dose": "2000mg daily", "timing": "With meals", "form": "tablet/powder", "with_food": true}'::jsonb,
true, true),

-- Vitamin B12
('Vitamin B12', 
'Essential for nerve function, red blood cell formation, and DNA synthesis',
ARRAY['Energy production', 'Nerve function', 'Red blood cell formation', 'Brain health', 'Mood support'],
ARRAY['Generally very safe', 'Rare allergic reactions possible', 'May interact with certain medications'],
ARRAY['Monitor if taking metformin', 'May mask folate deficiency'],
'{"min_dose": "250mcg daily", "max_dose": "1000mcg daily", "timing": "Morning", "form": "sublingual/tablet", "with_food": false}'::jsonb,
true, true),

-- Magnesium
('Magnesium Glycinate', 
'Highly absorbable form of magnesium for muscle, nerve, and sleep support',
ARRAY['Muscle relaxation', 'Nerve function', 'Sleep quality', 'Stress relief', 'Heart health', 'Bone health'],
ARRAY['May cause loose stools in high doses', 'Can cause drowsiness', 'May lower blood pressure'],
ARRAY['Reduce dose if have kidney disease', 'May interact with certain antibiotics'],
'{"min_dose": "200mg daily", "max_dose": "400mg daily", "timing": "Evening", "form": "capsule", "with_food": true}'::jsonb,
true, true),

-- Zinc
('Zinc', 
'Essential mineral for immune function, wound healing, and protein synthesis',
ARRAY['Immune support', 'Wound healing', 'Taste and smell', 'Protein synthesis', 'Skin health'],
ARRAY['May cause nausea on empty stomach', 'Can cause metallic taste', 'High doses may reduce copper absorption'],
ARRAY['Avoid high doses long-term', 'May interact with certain antibiotics'],
'{"min_dose": "8mg daily", "max_dose": "15mg daily", "timing": "With food", "form": "tablet/lozenge", "with_food": true}'::jsonb,
true, true),

-- Calcium
('Calcium', 
'Essential mineral for bone and teeth health, muscle function',
ARRAY['Bone health', 'Teeth health', 'Muscle contraction', 'Nerve signaling', 'Blood clotting'],
ARRAY['May cause constipation', 'Can cause bloating', 'May interfere with iron absorption'],
ARRAY['Avoid if history of kidney stones', 'May interact with certain heart medications'],
'{"min_dose": "500mg daily", "max_dose": "1200mg daily", "timing": "With meals", "form": "tablet", "with_food": true}'::jsonb,
true, true),

-- Iron
('Iron', 
'Essential for oxygen transport and energy production',
ARRAY['Prevents anemia', 'Energy production', 'Oxygen transport', 'Cognitive function', 'Immune support'],
ARRAY['Constipation', 'Stomach upset', 'Nausea', 'Dark stools', 'Metallic taste'],
ARRAY['Avoid if have hemochromatosis', 'Monitor levels regularly', 'May interact with certain medications'],
'{"min_dose": "18mg daily", "max_dose": "27mg daily", "timing": "Between meals with vitamin C", "form": "tablet", "with_food": false}'::jsonb,
true, true),

-- Whey Protein
('Whey Protein', 
'Complete protein powder for muscle building and recovery',
ARRAY['Muscle building', 'Post-workout recovery', 'Weight management', 'Immune support'],
ARRAY['May cause bloating in lactose intolerant individuals', 'Digestive upset possible', 'Kidney stress with excessive amounts'],
ARRAY['Avoid if lactose intolerant', 'Reduce if have kidney disease'],
'{"min_dose": "20g post-workout", "max_dose": "50g daily", "timing": "Post-workout or between meals", "form": "powder", "with_food": false}'::jsonb,
true, true),

-- Omega-3 Fish Oil
('Omega-3 Fish Oil', 
'Essential fatty acids for heart, brain, and joint health',
ARRAY['Heart health', 'Brain function', 'Joint health', 'Anti-inflammatory', 'Eye health'],
ARRAY['Fishy aftertaste', 'May cause bloating', 'Can increase bleeding risk', 'Digestive upset'],
ARRAY['Monitor if taking blood thinners', 'Avoid before surgery'],
'{"min_dose": "1000mg daily", "max_dose": "3000mg daily", "timing": "With meals", "form": "soft gel", "with_food": true}'::jsonb,
false, false),

-- Creatine
('Creatine Monohydrate', 
'Supports muscle energy, strength, and power output',
ARRAY['Increased muscle strength', 'Power output', 'Muscle recovery', 'Brain health', 'Exercise performance'],
ARRAY['May cause water retention', 'Initial weight gain from water', 'Rare digestive upset'],
ARRAY['Increase water intake', 'Monitor if have kidney concerns'],
'{"min_dose": "3g daily", "max_dose": "5g daily", "timing": "Post-workout or anytime", "form": "powder", "with_food": false}'::jsonb,
false, false),

-- Melatonin
('Melatonin', 
'Natural hormone that regulates sleep-wake cycles',
ARRAY['Sleep onset', 'Sleep quality', 'Jet lag recovery', 'Circadian rhythm regulation'],
ARRAY['Morning grogginess', 'Vivid dreams', 'Potential next-day drowsiness'],
ARRAY['Avoid if have autoimmune conditions', 'May interact with blood thinners'],
'{"min_dose": "0.5mg", "max_dose": "3mg", "timing": "30 minutes before bed", "form": "tablet", "with_food": false}'::jsonb,
false, false),

-- Turmeric Curcumin
('Turmeric Curcumin', 
'Potent anti-inflammatory compound from turmeric root',
ARRAY['Anti-inflammatory', 'Antioxidant support', 'Joint health', 'Brain health', 'Immune support'],
ARRAY['May cause stomach upset', 'Can increase bleeding risk', 'May cause heartburn'],
ARRAY['Avoid if have gallstones', 'Monitor if taking blood thinners'],
'{"min_dose": "500mg daily", "max_dose": "1000mg daily", "timing": "With meals and black pepper", "form": "capsule", "with_food": true}'::jsonb,
false, false),

-- L-Theanine
('L-Theanine', 
'Amino acid that promotes calm alertness without drowsiness',
ARRAY['Calm alertness', 'Stress reduction', 'Focus improvement', 'Sleep quality', 'Anxiety relief'],
ARRAY['Very few side effects', 'Rare headaches', 'Potential dizziness'],
ARRAY['Generally very safe', 'Monitor if taking sedatives'],
'{"min_dose": "100mg daily", "max_dose": "200mg daily", "timing": "Morning or as needed", "form": "capsule", "with_food": false}'::jsonb,
false, false),

-- Probiotics
('Probiotics', 
'Beneficial bacteria for digestive and immune health',
ARRAY['Digestive health', 'Immune support', 'Gut microbiome balance', 'Nutrient absorption'],
ARRAY['Initial bloating possible', 'Gas during adjustment period', 'Rare infections in immunocompromised'],
ARRAY['Avoid if severely immunocompromised', 'Monitor with certain medical conditions'],
'{"min_dose": "10 billion CFU daily", "max_dose": "50 billion CFU daily", "timing": "With or after meals", "form": "capsule", "with_food": true}'::jsonb,
true, true),

-- CoQ10
('CoQ10', 
'Supports cellular energy production and heart health',
ARRAY['Cellular energy', 'Heart health', 'Antioxidant support', 'Exercise performance'],
ARRAY['Stomach upset', 'Nausea', 'Diarrhea', 'Loss of appetite'],
ARRAY['May interact with blood thinners', 'Monitor if taking statins'],
'{"min_dose": "100mg daily", "max_dose": "300mg daily", "timing": "With meals", "form": "soft gel", "with_food": true}'::jsonb,
false, false);

-- Link supplements to health conditions with scoring from CSV data
INSERT INTO supplement_recommendations (health_tag_id, supplement_id, priority, score, rationale) VALUES

-- Low Energy recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'), 
 (SELECT id FROM supplements WHERE name = 'Multivitamin'), 1, 1,
 'Diets may lack essential nutrients; multivitamins provide B vitamins, vitamin D, and iron to fill nutritional gaps and support energy production'),

((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin B12'), 1, 1,
 'B12 deficiency causes fatigue and weakness due to poor oxygen delivery and is essential for energy metabolism'),

((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'), 
 (SELECT id FROM supplements WHERE name = 'Iron'), 1, 1,
 'Iron is essential for hemoglobin production and oxygen transport; deficiency causes fatigue'),

-- Sleep Quality Issues recommendations  
((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'), 
 (SELECT id FROM supplements WHERE name = 'Magnesium Glycinate'), 1, 1,
 'Supports relaxation, sleep onset, and quality through nervous system regulation'),

((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'), 
 (SELECT id FROM supplements WHERE name = 'Melatonin'), 1, 1,
 'Natural hormone that regulates circadian rhythm and improves sleep onset'),

-- Joint Support recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'), 
 (SELECT id FROM supplements WHERE name = 'Omega-3 Fish Oil'), 1, 1,
 'Anti-inflammatory properties help reduce joint discomfort and support mobility'),

((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'), 
 (SELECT id FROM supplements WHERE name = 'Turmeric Curcumin'), 1, 1,
 'Potent anti-inflammatory compound that helps reduce joint pain and inflammation'),

-- Physical Activity Support recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'), 
 (SELECT id FROM supplements WHERE name = 'Vitamin D3'), 1, 1,
 'Essential for bone health, muscle function, and immune system'),

((SELECT id FROM health_tags WHERE tag_name = 'Physical Activity Support'), 
 (SELECT id FROM supplements WHERE name = 'Creatine Monohydrate'), 1, 1,
 'Supports short bursts of energy, strength, and muscle recovery'),

-- Stress Management recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'Magnesium Glycinate'), 1, 1,
 'Stress depletes magnesium; it helps calm the nervous system and supports relaxation'),

((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'), 
 (SELECT id FROM supplements WHERE name = 'L-Theanine'), 1, 1,
 'Promotes calm alertness and reduces cortisol without causing drowsiness'),

-- Digestive Health Support recommendations
((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'), 
 (SELECT id FROM supplements WHERE name = 'Probiotics'), 1, 1,
 'Beneficial bacteria that support gut health, improve digestion, and boost immune function');

-- Update food recommendations table
INSERT INTO food_recommendations (health_tag_id, name, description, benefits, serving_suggestions) VALUES

-- Low Energy foods
((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'),
 'Iron-Rich Foods', 
 'Foods high in iron like spinach, lean red meat, lentils, and quinoa',
 ARRAY['Prevents anemia', 'Supports oxygen transport', 'Boosts energy', 'Improves concentration'],
 ARRAY['Include vitamin C foods to enhance absorption', 'Combine with citrus fruits', 'Cook in cast iron when possible']),

((SELECT id FROM health_tags WHERE tag_name = 'Low Energy'),
 'Complex Carbohydrates', 
 'Whole grains, sweet potatoes, oats, and brown rice for steady energy',
 ARRAY['Sustained energy release', 'Stable blood sugar', 'B-vitamin rich', 'Fiber for gut health'],
 ARRAY['Choose whole grain versions', 'Pair with protein', 'Eat regularly throughout day']),

-- Sleep support foods
((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'),
 'Tart Cherry Juice', 
 'Natural source of melatonin for better sleep',
 ARRAY['Natural melatonin', 'Improves sleep duration', 'Reduces inflammation', 'Antioxidant properties'],
 ARRAY['8oz 1-2 hours before bed', 'Choose unsweetened varieties', 'Can be diluted with water']),

((SELECT id FROM health_tags WHERE tag_name = 'Sleep Quality Issues'),
 'Magnesium-Rich Foods', 
 'Almonds, spinach, pumpkin seeds, and dark chocolate',
 ARRAY['Promotes relaxation', 'Better sleep quality', 'Muscle function', 'Nervous system support'],
 ARRAY['Snack on almonds 1 hour before bed', 'Add pumpkin seeds to evening meals', 'Choose 70%+ dark chocolate']),

-- Joint support foods
((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'),
 'Fatty Fish & Walnuts', 
 'Salmon, sardines, mackerel, and walnuts for omega-3 fatty acids',
 ARRAY['Anti-inflammatory omega-3s', 'Joint support', 'Heart healthy', 'Brain food'],
 ARRAY['Aim for 2-3 servings per week', 'Choose wild-caught when possible', 'Add walnuts to salads and yogurt']),

((SELECT id FROM health_tags WHERE tag_name = 'Joint Support Needed'),
 'Colorful Berries', 
 'Blueberries, cherries, and strawberries rich in anthocyanins',
 ARRAY['Antioxidant compounds', 'Anti-inflammatory', 'Vitamin C', 'Fiber'],
 ARRAY['1 cup daily fresh or frozen', 'Mix into smoothies', 'Add to breakfast cereals']),

-- Stress management foods
((SELECT id FROM health_tags WHERE tag_name = 'Stress Management'),
 'Green Tea & Dark Chocolate', 
 'Natural stress-reducing compounds and healthy treats',
 ARRAY['L-theanine for calm', 'Antioxidants', 'Mood support', 'Cognitive benefits'],
 ARRAY['2-3 cups green tea daily', '1 oz dark chocolate (70%+)', 'Avoid late in day for sleep']),

-- Digestive health foods
((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'),
 'Fermented Foods', 
 'Yogurt, kefir, sauerkraut, kimchi, and kombucha',
 ARRAY['Natural probiotics', 'Digestive enzymes', 'Immune support', 'Nutrient density'],
 ARRAY['Include one serving daily', 'Start small and increase gradually', 'Choose unpasteurized when possible']),

((SELECT id FROM health_tags WHERE tag_name = 'Digestive Health Support'),
 'Fiber-Rich Foods', 
 'Vegetables, fruits, legumes, and whole grains',
 ARRAY['Gut microbiome support', 'Regular digestion', 'Nutrient absorption', 'Satiety'],
 ARRAY['Increase gradually to avoid bloating', 'Drink plenty of water', 'Aim for 25-35g daily']);

-- Verification
DO $$
DECLARE
    supplement_count INTEGER;
    recommendation_count INTEGER;
    food_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO supplement_count FROM supplements;
    SELECT COUNT(*) INTO recommendation_count FROM supplement_recommendations;
    SELECT COUNT(*) INTO food_count FROM food_recommendations;
    
    RAISE NOTICE 'Enhanced supplement data seeding complete:';
    RAISE NOTICE '- Supplements: %', supplement_count;
    RAISE NOTICE '- Supplement Recommendations: %', recommendation_count;
    RAISE NOTICE '- Food Recommendations: %', food_count;
END $$;