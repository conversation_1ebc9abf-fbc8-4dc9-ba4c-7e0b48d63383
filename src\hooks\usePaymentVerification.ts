import { supabase } from '@/lib/supabase'

interface PaymentVerificationResponse {
  subscription_active: boolean
  user_exists: boolean
  timestamp: string
  error?: string
  details?: string
}

interface PaymentVerificationResult {
  success: boolean
  subscriptionActive: boolean
  userExists: boolean
  error?: string
}

export const usePaymentVerification = () => {
  const verifyPayment = async (sessionId: string, customerEmail?: string): Promise<PaymentVerificationResult> => {
    try {
      console.log('Calling verify-payment-status function with:', { sessionId, customerEmail })
      
      const { data, error } = await supabase.functions.invoke('verify-payment-status', {
        body: { 
          session_id: sessionId,
          customer_email: customerEmail 
        }
      })

      if (error) {
        console.error('Payment verification function error:', error)
        return {
          success: false,
          subscriptionActive: false,
          userExists: false,
          error: error.message
        }
      }

      const response = data as PaymentVerificationResponse
      console.log('Payment verification response:', response)

      return {
        success: true,
        subscriptionActive: response.subscription_active || false,
        userExists: response.user_exists || false,
        error: response.error
      }
    } catch (error) {
      console.error('Payment verification unexpected error:', error)
      return {
        success: false,
        subscriptionActive: false,
        userExists: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  return { verifyPayment }
}