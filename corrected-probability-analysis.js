// CORRECTED Analysis: Using the actual coverage scoring logic from updated-edge-function.js
// Coverage Score = 100 + (coverageConditions.length * 20)

// Your conditions
const userConditions = ["Low Energy & Blood Sugar", "Cardiovascular Health Support"];

// Supplements with CORRECTED coverage scores
const AVAILABLE_SUPPLEMENTS = [
  // Ceylon Cinnamon covers BOTH your conditions
  { 
    name: "Ceylon Cinnamon", 
    condition_names: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"],
    coverageConditions: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"], // covers BOTH
    coverageScore: 100 + (2 * 20), // 140 - MULTI-CONDITION BONUS!
    type: "MULTI-CONDITION" 
  },
  
  // B-Complex covers only 1 of your conditions
  { 
    name: "B-Complex Vitamins", 
    condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus"],
    coverageConditions: ["Low Energy & Blood Sugar"], // only covers 1 of your conditions
    coverageScore: 100 + (1 * 20), // 120 - single condition
    type: "SINGLE-CONDITION" 
  },
  
  // Chromium covers only 1 of your conditions
  { 
    name: "Chromium Picolinate", 
    condition_names: ["Low Energy & Blood Sugar", "Weight Management Support"],
    coverageConditions: ["Low Energy & Blood Sugar"], // only covers 1 of your conditions
    coverageScore: 100 + (1 * 20), // 120 - single condition
    type: "SINGLE-CONDITION" 
  },
  
  // Rhodiola covers only 1 of your conditions
  { 
    name: "Rhodiola Rosea", 
    condition_names: ["Physical Performance", "Low Energy & Blood Sugar"],
    coverageConditions: ["Low Energy & Blood Sugar"], // only covers 1 of your conditions
    coverageScore: 100 + (1 * 20), // 120 - single condition
    type: "SINGLE-CONDITION" 
  },
  
  // Biotin covers only 1 of your conditions
  { 
    name: "Biotin", 
    condition_names: ["Hair & Skin Support", "Low Energy & Blood Sugar"],
    coverageConditions: ["Low Energy & Blood Sugar"], // only covers 1 of your conditions
    coverageScore: 100 + (1 * 20), // 120 - single condition
    type: "SINGLE-CONDITION" 
  },
  
  // Hawthorn covers only 1 of your conditions
  { 
    name: "Hawthorn Berry", 
    condition_names: ["Cardiovascular Health Support", "Digestive Health Support"],
    coverageConditions: ["Cardiovascular Health Support"], // only covers 1 of your conditions
    coverageScore: 100 + (1 * 20), // 120 - single condition
    type: "SINGLE-CONDITION" 
  },
  
  // Green Tea covers only 1 of your conditions
  { 
    name: "Green Tea Extract", 
    condition_names: ["Weight Management Support", "Cardiovascular Health Support"],
    coverageConditions: ["Cardiovascular Health Support"], // only covers 1 of your conditions
    coverageScore: 100 + (1 * 20), // 120 - single condition
    type: "SINGLE-CONDITION" 
  }
];

// Weighted Random Selection Function
function weightedRandomSelection(supplements) {
  if (!supplements || supplements.length === 0) return null;
  if (supplements.length === 1) return supplements[0];
  
  const totalWeight = supplements.reduce((sum, item) => sum + item.coverageScore, 0);
  let random = Math.random() * totalWeight;
  
  for (const item of supplements) {
    random -= item.coverageScore;
    if (random <= 0) return item;
  }
  return supplements[supplements.length - 1];
}

// Calculate probabilities with CORRECTED scores
const totalWeight = AVAILABLE_SUPPLEMENTS.reduce((sum, s) => sum + s.coverageScore, 0);

console.log("🩸❤️ CORRECTED SUPPLEMENT PROBABILITY ANALYSIS");
console.log("Conditions: Low Blood Sugar + Cardiovascular Problems");
console.log("Using ACTUAL coverage scoring: 100 + (conditions_covered * 20)");
console.log("=".repeat(75));

console.log("\n📊 CORRECTED COVERAGE SCORES & SELECTION CHANCES:");
console.log("-".repeat(75));

AVAILABLE_SUPPLEMENTS
  .sort((a, b) => b.coverageScore - a.coverageScore)
  .forEach(supplement => {
    const probability = (supplement.coverageScore / totalWeight * 100).toFixed(1);
    const typeIcon = supplement.type === "MULTI-CONDITION" ? "⭐" : "🔸";
    
    console.log(`${typeIcon} ${supplement.name.padEnd(20)} | Score: ${supplement.coverageScore.toString().padEnd(3)} | Chance: ${probability.padEnd(5)}%`);
    console.log(`   Covers: ${supplement.coverageConditions.join(" + ")}`);
    console.log("");
  });

console.log("🎯 CORRECTED PROBABILITY RANKING:");
console.log("-".repeat(50));

const probabilityRanking = AVAILABLE_SUPPLEMENTS
  .map(s => ({
    name: s.name,
    probability: (s.coverageScore / totalWeight * 100).toFixed(1),
    score: s.coverageScore,
    type: s.type,
    conditionsCovered: s.coverageConditions.length
  }))
  .sort((a, b) => b.probability - a.probability);

probabilityRanking.forEach((item, index) => {
  const medal = index === 0 ? "🥇" : index === 1 ? "🥈" : index === 2 ? "🥉" : `${index + 1}.`;
  const typeIcon = item.type === "MULTI-CONDITION" ? "⭐" : "🔸";
  console.log(`${medal} ${item.name} - ${item.probability}% chance ${typeIcon} (covers ${item.conditionsCovered} conditions)`);
});

// Simulation with corrected scores
console.log("\n🧪 CORRECTED SIMULATION RESULTS (10,000 selections):");
console.log("-".repeat(60));

const simulationResults = {};
const simulations = 10000;

for (let i = 0; i < simulations; i++) {
  const selected = weightedRandomSelection(AVAILABLE_SUPPLEMENTS);
  simulationResults[selected.name] = (simulationResults[selected.name] || 0) + 1;
}

Object.entries(simulationResults)
  .sort((a, b) => b[1] - a[1])
  .forEach(([name, count]) => {
    const actualPercent = (count / simulations * 100).toFixed(1);
    const supplement = AVAILABLE_SUPPLEMENTS.find(s => s.name === name);
    const expectedPercent = (supplement.coverageScore / totalWeight * 100).toFixed(1);
    const deviation = Math.abs(actualPercent - expectedPercent).toFixed(1);
    
    console.log(`${name.padEnd(20)} | ${count.toString().padEnd(4)} times | ${actualPercent.padEnd(5)}% | Expected: ${expectedPercent}% | ±${deviation}%`);
  });

console.log("\n" + "=".repeat(75));
console.log("🎯 CORRECTED KEY INSIGHTS:");
console.log("=".repeat(75));

const ceyalonCinnamon = probabilityRanking[0];
console.log(`🥇 CEYLON CINNAMON IS THE CLEAR WINNER!`);
console.log(`   • ${ceyalonCinnamon.probability}% selection chance (highest)`);
console.log(`   • Covers BOTH your conditions (gets multi-condition bonus)`);
console.log(`   • Score: 140 vs others at 120 (16.7% higher)`);

console.log(`\n📊 SCORE BREAKDOWN:`);
console.log(`   • Ceylon Cinnamon: 100 + (2 conditions × 20) = 140 ⭐`);
console.log(`   • All others: 100 + (1 condition × 20) = 120 🔸`);

console.log(`\n🎯 WHY CEYLON CINNAMON WINS:`);
console.log(`   • Multi-condition bonus: +20 points for covering both conditions`);
console.log(`   • Higher probability: ~${ceyalonCinnamon.probability}% vs ~${probabilityRanking[1].probability}% for others`);
console.log(`   • Most efficient: Addresses both your health concerns with one supplement`);

const advantage = ((parseFloat(ceyalonCinnamon.probability) / parseFloat(probabilityRanking[1].probability)) - 1) * 100;
console.log(`\n📈 CEYLON CINNAMON ADVANTAGE:`);
console.log(`   • ${advantage.toFixed(1)}% more likely to be selected than single-condition supplements`);
console.log(`   • Perfect match for your specific health profile!`);
