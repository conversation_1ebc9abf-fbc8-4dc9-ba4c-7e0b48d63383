import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from '../_shared/cors.ts'
import Stripe from 'https://esm.sh/stripe@14.14.0?target=deno'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
})

// Helper function to encode quiz data for URL
function encodeQuizDataForURL(answers: Record<string, any>, email: string = ''): string {
  try {
    const data = { answers, email, timestamp: Date.now() }
    const jsonString = JSON.stringify(data)
    const encoded = btoa(encodeURIComponent(jsonString))
    return encoded
  } catch (error) {
    console.error('Error encoding quiz data for URL:', error)
    return ''
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { metadata = {}, customer_email, quiz_answers = {} } = await req.json()

    console.log('Creating checkout session with:', { customer_email, metadata, quiz_answers_count: Object.keys(quiz_answers).length })

    // Encode quiz data for success URL
    const encodedQuizData = encodeQuizDataForURL(quiz_answers, customer_email)
    const successUrl = encodedQuizData 
      ? `${req.headers.get('origin')}/payment-success?session_id={CHECKOUT_SESSION_ID}&qd=${encodedQuizData}`
      : `${req.headers.get('origin')}/payment-success?session_id={CHECKOUT_SESSION_ID}`

    console.log('Success URL with quiz data:', successUrl.substring(0, 150) + '...')

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: 'Premium Health Report',
              description: '7-Day Trial + Monthly Subscription',
            },
            unit_amount: 1999, // $19.99 in cents
            recurring: {
              interval: 'month',
            },
          },
          quantity: 1,
        },
      ],
      mode: 'subscription',
      customer_email: customer_email,
      subscription_data: {
        trial_period_days: 7,
        metadata: {
          customer_email: customer_email,
          ...metadata,
        },
      },
      success_url: successUrl,
      cancel_url: `${req.headers.get('origin')}/payment`,
      metadata: {
        product: 'health_report',
        customer_email: customer_email,
        ...metadata,
      },
      billing_address_collection: 'required',
    })

    return new Response(
      JSON.stringify({
        checkout_url: session.url,
        session_id: session.id,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Checkout session creation error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to create checkout session', details: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})