import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import Stripe from 'https://esm.sh/stripe@14.14.0?target=deno'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
})

const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Verify the user's JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    
    if (authError || !user) {
      console.error('Authentication error:', authError)
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const { subscriptionId, stripeId } = await req.json()
    
    if (!subscriptionId || !stripeId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: subscriptionId and stripeId' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Cancel subscription request:', { 
      userId: user.id, 
      subscriptionId, 
      stripeId: stripeId.substring(0, 10) + '...' 
    })

    // Verify user owns this subscription
    const { data: subscription, error: subError } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('id', subscriptionId)
      .eq('user_id', user.id)
      .eq('stripe_id', stripeId)
      .single()

    if (subError || !subscription) {
      console.error('Subscription verification error:', subError)
      return new Response(
        JSON.stringify({ error: 'Subscription not found or unauthorized' }),
        { 
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Check if subscription is already canceled
    if (subscription.status === 'canceled') {
      return new Response(
        JSON.stringify({ error: 'Subscription is already canceled' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Canceling Stripe subscription:', stripeId)

    // Cancel the subscription in Stripe
    const canceledSubscription = await stripe.subscriptions.cancel(stripeId)
    
    console.log('Stripe cancellation successful:', {
      id: canceledSubscription.id,
      status: canceledSubscription.status,
      canceled_at: canceledSubscription.canceled_at
    })

    // Update the subscription status in the database
    const { error: updateError } = await supabaseAdmin
      .from('subscriptions')
      .update({ 
        status: 'canceled',
        canceled_at: new Date().toISOString()
      })
      .eq('id', subscriptionId)
      .eq('user_id', user.id)

    if (updateError) {
      console.error('Database update error:', updateError)
      // Log this for manual intervention - subscription was canceled in Stripe but not updated in DB
      console.error('CRITICAL: Subscription canceled in Stripe but DB update failed:', {
        userId: user.id,
        subscriptionId,
        stripeId,
        updateError
      })
      
      return new Response(
        JSON.stringify({ 
          error: 'Subscription canceled in Stripe but database update failed. Please contact support.',
          partial_success: true
        }),
        { 
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Subscription cancellation completed successfully:', {
      userId: user.id,
      subscriptionId,
      stripeId: stripeId.substring(0, 10) + '...'
    })

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Subscription canceled successfully',
        canceled_at: canceledSubscription.canceled_at
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Cancel subscription error:', error)
    
    // Handle specific Stripe errors
    if (error.type === 'StripeInvalidRequestError') {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid subscription ID or subscription not found in Stripe',
          details: error.message 
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    return new Response(
      JSON.stringify({ 
        error: 'Failed to cancel subscription', 
        details: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})