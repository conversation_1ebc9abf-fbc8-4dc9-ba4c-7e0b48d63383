# Supabase Database Setup Guide

## Step-by-Step Instructions

### 1. Go to your Supabase Dashboard
Open: https://supabase.com/dashboard/project/zjeqsipsrjeojscjnihe

### 2. Navigate to SQL Editor
- Click on "SQL Editor" in the left sidebar
- Click "New Query" to create a new SQL query

### 3. Run the Schema Files in Order

**IMPORTANT: Run these in the exact order listed!**

#### Step 1: Core Schema
- Copy the entire contents of `supabase/01_schema_core.sql`
- Paste into SQL Editor
- Click "Run" (should show success messages)

#### Step 2: Security Policies  
- Copy the entire contents of `supabase/02_schema_security.sql`
- Paste into SQL Editor
- Click "Run" (should show success messages)

#### Step 3: Functions & Triggers
- Copy the entire contents of `supabase/03_schema_functions.sql` 
- Paste into SQL Editor
- Click "Run" (should show success messages)

#### Step 4: Seed Data
- Copy the entire contents of `supabase/04_seed_data.sql`
- Paste into SQL Editor  
- Click "Run" (should show data counts at the end)

### 4. Verify Setup

After running all files, you should see:
- ✅ 7 tables created (profiles, questions, health_tags, recommendations, responses, reports, subscriptions)
- ✅ 10 quiz questions
- ✅ 6 health tag categories  
- ✅ 20+ supplement and food recommendations

### 5. Check Your Tables

Go to "Table Editor" in Supabase dashboard and verify:
- **questions** table has 10 rows
- **health_tags** table has 6 rows
- **recommendations** table has 20+ rows

## Troubleshooting

### If you get errors:
1. **"relation already exists"** - This is OK, the scripts handle existing tables
2. **"permission denied"** - Make sure you're logged into your Supabase account
3. **"function does not exist"** - Run the files in the correct order

### If seed data fails:
- Make sure you ran `01_schema_core.sql` first
- Check that all tables were created successfully
- Try running `04_seed_data.sql` again

## What This Sets Up

- **Complete quiz system** with 10 health questions
- **6 health categories** (energy, sleep, joints, activity, stress, digestion)
- **12 targeted supplements** with dosages and timing
- **10+ food recommendations** with nutritional benefits
- **User authentication** with profiles and permissions
- **Admin capabilities** for managing content
- **Automatic triggers** for user profile creation

Once complete, the app will automatically switch from static data to your Supabase database!