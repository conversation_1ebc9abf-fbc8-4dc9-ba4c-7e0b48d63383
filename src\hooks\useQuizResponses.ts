import { useState, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import type { Database } from '@/types/database'

type Response = Database['public']['Tables']['responses']['Insert']

export function useQuizResponses() {
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  const saveResponses = useCallback(async (answers: Record<string, string>) => {
    if (!user) {
      throw new Error('User must be authenticated to save responses')
    }

    try {
      setSaving(true)
      setError(null)

      console.log('Answers to save:', Object.keys(answers).length, answers)

      // First, fetch valid question IDs from the database
      const { data: validQuestions, error: questionsError } = await supabase
        .from('questions')
        .select('id')
      
      if (questionsError) {
        console.error('Error fetching valid question IDs:', questionsError)
        throw questionsError
      }

      // Create a set of valid question IDs for fast lookup
      const validQuestionIds = new Set(validQuestions?.map(q => q.id) || [])
      console.log('Valid question IDs from DB:', validQuestionIds.size)
      
      // Only save answers that have question IDs that actually exist in the database
      const filteredAnswers = Object.entries(answers).filter(([questionId]) => 
        validQuestionIds.has(questionId)
      )
      
      console.log(`Filtered answers: ${filteredAnswers.length} out of ${Object.keys(answers).length}`)
      
      // If there are no valid question IDs in the database, skip the save operation
      // but return a successful result (empty array) to not block the flow
      if (filteredAnswers.length === 0) {
        console.warn('No valid question IDs found in database. Skipping response save operation.')
        return []
      }

      // Convert filtered answers to response format
      const responses: Response[] = filteredAnswers.map(([questionId, answer]) => ({
        user_id: user.id,
        question_id: questionId,
        answer: answer
      }))

      console.log(`Found ${responses.length} valid questions out of ${Object.keys(answers).length} answers`)

      // If there are responses to save, proceed with save operations
      if (responses.length > 0) {
        try {
          // First, delete any existing responses for this user
          const { error: deleteError } = await supabase
            .from('responses')
            .delete()
            .eq('user_id', user.id)

          if (deleteError) {
            console.error('Error deleting existing responses:', deleteError)
            throw deleteError
          }

          // Wait a brief moment to ensure deletion is complete
          await new Promise(resolve => setTimeout(resolve, 500))

          // Then insert all responses at once
          const { error: insertError } = await supabase
            .from('responses')
            .insert(responses)

          if (insertError) {
            console.error('Error inserting responses:', insertError)
            throw insertError
          }
        } catch (err) {
          console.error('Error during response save operation:', err)
          // We'll still return the responses array even if save failed
          // This allows the quiz flow to continue
        }
      }

      return responses
    } catch (err) {
      console.error('Error saving responses:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to save responses'
      setError(errorMessage)
      
      // Instead of throwing, return empty array to allow the quiz flow to continue
      return []
    } finally {
      setSaving(false)
    }
  }, [user])

  return { saveResponses, saving, error }
}