// Test to verify cardiovascular and weight management supplements are working
// This tests the fix for the condition_names arrays

// Mock data from updated-edge-function.js
const SUPPLEMENTS = [
  {
    name: "Ceylon Cinnamon",
    condition_names: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"],
    description: "Natural blood sugar support and circulation enhancement",
    benefits: ["Blood sugar regulation", "Improved circulation", "Anti-inflammatory", "Antioxidant"]
  },
  {
    name: "Chromium Picolinate", 
    condition_names: ["Low Energy & Blood Sugar", "Weight Management Support"],
    description: "Trace mineral that supports healthy blood sugar metabolism",
    benefits: ["Blood sugar regulation", "Reduced sugar cravings", "Improved insulin sensitivity", "Weight management"]
  },
  {
    name: "Hawthorn Berry",
    condition_names: ["Cardiovascular Health Support", "Digestive Health Support"],
    description: "Traditional herbal supplement that supports cardiovascular health",
    benefits: ["Supports heart function", "Promotes healthy circulation", "May help maintain normal blood pressure"]
  },
  {
    name: "Green Tea Extract",
    condition_names: ["Weight Management Support", "Cardiovascular Health Support"],
    description: "Concentrated green tea extract standardized for EGCG",
    benefits: ["Supports metabolism", "May boost fat burning", "Antioxidant protection", "Natural energy support"]
  }
];

const FOOD_RECOMMENDATIONS = [
  {
    name: "Salmon & Leafy Greens",
    condition_names: ["Cardiovascular Health Support"],
    description: "Wild-caught salmon and dark leafy greens for heart health",
    benefits: ["Supports heart health", "Promotes healthy circulation", "Anti-inflammatory omega-3s"]
  },
  {
    name: "Green Tea & Lean Proteins",
    condition_names: ["Weight Management Support"],
    description: "Green tea combined with lean proteins for metabolism support",
    benefits: ["Boosts metabolism", "Promotes satiety", "Supports lean muscle", "Natural fat burning"]
  }
];

// Test function to find supplements for conditions
function findSupplementsForConditions(conditions) {
  return SUPPLEMENTS.filter(supplement => 
    supplement.condition_names.some(condition => conditions.includes(condition))
  );
}

function findFoodsForConditions(conditions) {
  return FOOD_RECOMMENDATIONS.filter(food => 
    food.condition_names.some(condition => conditions.includes(condition))
  );
}

console.log("🧪 TESTING CARDIOVASCULAR & WEIGHT MANAGEMENT SUPPLEMENT FIX");
console.log("=".repeat(70));

// Test 1: Cardiovascular Health Support
console.log("\n❤️ TEST 1: CARDIOVASCULAR HEALTH SUPPORT");
console.log("-".repeat(50));
const cardioConditions = ["Cardiovascular Health Support"];
const cardioSupplements = findSupplementsForConditions(cardioConditions);
const cardioFoods = findFoodsForConditions(cardioConditions);

console.log(`Detected conditions: ${cardioConditions.join(", ")}`);
console.log(`\nSupplements found: ${cardioSupplements.length}`);
cardioSupplements.forEach(s => {
  console.log(`  ✅ ${s.name} - ${s.condition_names.join(", ")}`);
});

console.log(`\nFoods found: ${cardioFoods.length}`);
cardioFoods.forEach(f => {
  console.log(`  🥗 ${f.name} - ${f.condition_names.join(", ")}`);
});

// Test 2: Weight Management Support
console.log("\n⚖️ TEST 2: WEIGHT MANAGEMENT SUPPORT");
console.log("-".repeat(50));
const weightConditions = ["Weight Management Support"];
const weightSupplements = findSupplementsForConditions(weightConditions);
const weightFoods = findFoodsForConditions(weightConditions);

console.log(`Detected conditions: ${weightConditions.join(", ")}`);
console.log(`\nSupplements found: ${weightSupplements.length}`);
weightSupplements.forEach(s => {
  console.log(`  ✅ ${s.name} - ${s.condition_names.join(", ")}`);
});

console.log(`\nFoods found: ${weightFoods.length}`);
weightFoods.forEach(f => {
  console.log(`  🥗 ${f.name} - ${f.condition_names.join(", ")}`);
});

// Test 3: Combined conditions (user has both)
console.log("\n❤️⚖️ TEST 3: BOTH CARDIOVASCULAR & WEIGHT MANAGEMENT");
console.log("-".repeat(50));
const combinedConditions = ["Cardiovascular Health Support", "Weight Management Support"];
const combinedSupplements = findSupplementsForConditions(combinedConditions);
const combinedFoods = findFoodsForConditions(combinedConditions);

console.log(`Detected conditions: ${combinedConditions.join(", ")}`);
console.log(`\nSupplements found: ${combinedSupplements.length}`);
combinedSupplements.forEach(s => {
  const matchingConditions = s.condition_names.filter(c => combinedConditions.includes(c));
  const isMultiCondition = matchingConditions.length > 1 ? " ⭐ MULTI-CONDITION" : "";
  console.log(`  ✅ ${s.name} - ${matchingConditions.join(", ")}${isMultiCondition}`);
});

console.log(`\nFoods found: ${combinedFoods.length}`);
combinedFoods.forEach(f => {
  const matchingConditions = f.condition_names.filter(c => combinedConditions.includes(c));
  console.log(`  🥗 ${f.name} - ${matchingConditions.join(", ")}`);
});

// Test 4: Multi-condition supplements analysis
console.log("\n⭐ TEST 4: MULTI-CONDITION SUPPLEMENTS ANALYSIS");
console.log("-".repeat(50));
const allConditions = ["Low Energy & Blood Sugar", "Cardiovascular Health Support", "Weight Management Support"];
const allSupplements = findSupplementsForConditions(allConditions);

console.log("Multi-condition supplements (cover multiple health areas):");
allSupplements.forEach(s => {
  const matchingConditions = s.condition_names.filter(c => allConditions.includes(c));
  if (matchingConditions.length > 1) {
    console.log(`  ⭐ ${s.name} covers: ${matchingConditions.join(" + ")}`);
  }
});

console.log("\n" + "=".repeat(70));
console.log("🎯 SUMMARY");
console.log("=".repeat(70));
console.log("✅ Cardiovascular Health Support: Should show Ceylon Cinnamon, Hawthorn Berry, Green Tea Extract");
console.log("✅ Weight Management Support: Should show Chromium Picolinate, Green Tea Extract");
console.log("✅ Multi-condition supplements: Ceylon Cinnamon (cardio), Chromium Picolinate (weight), Green Tea Extract (both)");
console.log("✅ Foods: Salmon & Leafy Greens (cardio), Green Tea & Lean Proteins (weight)");

if (cardioSupplements.length >= 3 && weightSupplements.length >= 2) {
  console.log("\n🎉 SUCCESS: Fix is working! Supplements are being detected for both conditions.");
} else {
  console.log("\n❌ ISSUE: Some supplements are still missing. Check condition_names arrays.");
}
