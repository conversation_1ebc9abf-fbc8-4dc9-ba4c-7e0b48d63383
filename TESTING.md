# Quiz Testing Documentation

This project now includes comprehensive testing infrastructure for the quiz and recommendation system.

## Quick Start

```bash
# Install dependencies (if not already done)
npm install

# Run all tests
npm run test

# Test all quiz scenarios
npm run test:quiz --all

# Test a specific scenario
npm run test:quiz --scenario "Low Energy Person"

# List available scenarios
npm run test:quiz --list
```

## Testing Structure

### 1. Test Framework Setup
- **Vitest**: Modern, fast test runner
- **React Testing Library**: For component testing
- **JSDOM**: Browser environment simulation

### 2. Predefined Quiz Scenarios
Located in `src/test-data/predefinedQuizScenarios.ts`, includes:

- **Low Energy Person**: Fatigue, poor concentration, energy crashes
- **Joint Issues Person**: Joint pain, stiffness, mobility issues
- **High Stress Person**: Chronic stress, poor work-life balance
- **Healthy Person**: Minimal health concerns (baseline)
- **Multiple Issues Person**: Overlapping health concerns
- **Active Person with Recovery Issues**: Exercise but poor recovery
- **Digestive Issues Person**: Digestive problems
- **Immune System Issues Person**: Frequent illness, poor immunity

Each scenario includes:
- Realistic quiz answers
- Expected health conditions
- Expected supplement types
- Description of the health profile

### 3. Core Tests

#### Quiz Logic Tests (`src/test/quiz.test.ts`)
- Tests `processQuizAnswers()` function
- Validates condition identification
- Checks recommendation generation
- Verifies priority scoring
- Tests all predefined scenarios

#### React Hook Tests (`src/test/hooks.test.tsx`)
- Tests `useQuestionsStatic` hook
- Tests `useQuizResults` hook (mocked)
- Validates hook integration

### 4. Testing Utilities

#### QuizTester Class (`src/test-utils/quizTester.ts`)
Programmatic testing interface with methods:

- `testScenario(scenario)`: Test single scenario
- `testAllScenarios()`: Test all scenarios
- `compareRecommendations()`: Compare expected vs actual
- `generateDetailedReport()`: Create detailed test reports
- `generateSummaryReport()`: Create summary reports

#### Helper Functions
- `quickTestScenario(name)`: Quick single scenario test
- `quickTestAllScenarios()`: Quick all scenarios test

### 5. CLI Testing Tool

The `npm run test:quiz` command provides a powerful CLI for testing scenarios:

```bash
# Test all scenarios with summary
npm run test:quiz --all

# Test specific scenario with details
npm run test:quiz --scenario "Low Energy Person"

# List all available scenarios
npm run test:quiz --list

# Test all with detailed reports
npm run test:quiz --all --detailed

# Show performance metrics
npm run test:quiz --all --performance

# Compare expected vs actual recommendations
npm run test:quiz --scenario "Healthy Person" --compare

# Show help
npm run test:quiz --help
```

## Usage Examples

### 1. Running Standard Tests
```bash
# Run all unit tests
npm run test

# Run tests in watch mode
npm run test

# Run tests once
npm run test:run

# Open test UI
npm run test:ui
```

### 2. Testing Quiz Scenarios

```bash
# Quick test of all scenarios
npm run test:quiz --all

# Detailed test of specific scenario
npm run test:quiz --scenario "Multiple Issues Person" --detailed

# Performance analysis
npm run test:quiz --all --performance

# Check recommendation accuracy
npm run test:quiz --all --compare
```

### 3. Development Workflow

When developing the recommendation algorithm:

1. **Run baseline tests**: `npm run test:quiz --all`
2. **Make algorithm changes**
3. **Test specific scenarios**: `npm run test:quiz --scenario "Low Energy Person"`
4. **Compare results**: `npm run test:quiz --scenario "Low Energy Person" --compare`
5. **Run full suite**: `npm run test:quiz --all`

### 4. Programmatic Testing

You can also use the testing utilities in your code:

```typescript
import { QuizTester, quickTestAllScenarios } from './src/test-utils/quizTester'
import { QUIZ_SCENARIOS } from './src/test-data/predefinedQuizScenarios'

// Test all scenarios programmatically
const summary = QuizTester.testAllScenarios()
console.log(`${summary.successfulTests}/${summary.totalScenarios} tests passed`)

// Test specific scenario
const result = QuizTester.testScenario(QUIZ_SCENARIOS[0])
console.log(QuizTester.generateDetailedReport(result))
```

## Adding New Test Scenarios

To add a new test scenario:

1. Open `src/test-data/predefinedQuizScenarios.ts`
2. Add your scenario to the `QUIZ_SCENARIOS` array:

```typescript
{
  name: 'Your New Scenario',
  description: 'Description of the health profile',
  answers: {
    // Map of question IDs to answers
    '550e8400-e29b-41d4-a716-446655440003': 'Yes',
    // ... other answers
  },
  expectedConditions: ['Expected Health Condition'],
  expectedSupplementTypes: ['Expected Supplement Name'],
}
```

3. Run the tests to validate: `npm run test:quiz --scenario "Your New Scenario"`

## Interpreting Test Results

### Success Indicators ✅
- All expected conditions identified
- Recommendations generated
- No errors during processing

### Failure Indicators ❌
- Expected conditions missing
- No recommendations generated
- Processing errors
- Unexpected conditions found

### Performance Metrics ⚡
- Execution time per scenario
- Average processing time
- Recommendations count
- Conditions identified

## Files Overview

```
src/
├── test/
│   ├── setup.ts              # Test environment setup
│   ├── quiz.test.ts           # Core quiz logic tests
│   └── hooks.test.tsx         # React hooks tests
├── test-data/
│   └── predefinedQuizScenarios.ts  # Test scenarios
└── test-utils/
    └── quizTester.ts          # Testing utilities

scripts/
└── test-quiz-scenarios.ts     # CLI testing tool

vitest.config.ts              # Vitest configuration
```

## Benefits

This testing setup provides:

1. **Rapid Development**: Test algorithm changes without UI interaction
2. **Regression Prevention**: Ensure changes don't break existing scenarios
3. **Performance Monitoring**: Track recommendation generation speed
4. **Algorithm Validation**: Verify expected vs actual recommendations
5. **Comprehensive Coverage**: Test various health profiles systematically
6. **Easy Debugging**: Detailed reports help identify issues quickly

## Continuous Integration

The tests can be integrated into CI/CD pipelines:

```yaml
# .github/workflows/test.yml
- name: Run Quiz Tests
  run: |
    npm run test:run
    npm run test:quiz --all
```