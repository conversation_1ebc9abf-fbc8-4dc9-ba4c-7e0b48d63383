import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { X, ExternalLink, FileText, Shield } from 'lucide-react'

interface LegalModalProps {
  isOpen: boolean
  onClose: () => void
  type: 'terms' | 'privacy'
  onAccept?: () => void
  showAcceptButton?: boolean
}

export function LegalModal({ isOpen, onClose, type, onAccept, showAcceptButton = false }: LegalModalProps) {
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false)

  if (!isOpen) return null

  const isTerms = type === 'terms'
  const IconComponent = isTerms ? FileText : Shield
  const title = isTerms ? 'Terms of Service' : 'Privacy Policy'
  const description = isTerms 
    ? 'Please review our terms and conditions'
    : 'Learn how we protect and use your information'

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget
    const atBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + 50
    if (atBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true)
    }
  }

  const handleOpenFullPage = () => {
    const url = isTerms ? '/terms' : '/privacy'
    window.open(url, '_blank')
  }

  const handleAccept = () => {
    if (onAccept) {
      onAccept()
    }
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] flex flex-col">
        <CardHeader className="flex-shrink-0 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconComponent className="w-6 h-6 text-blue-600" />
              <div>
                <CardTitle className="text-xl">{title}</CardTitle>
                <p className="text-sm text-slate-600 mt-1">{description}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleOpenFullPage}
                className="flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Full Page
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="p-2"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-hidden p-0">
          <div 
            className="h-full overflow-y-auto p-6"
            onScroll={handleScroll}
          >
            <div className="prose prose-slate max-w-none">
              {isTerms ? (
                <TermsContent />
              ) : (
                <PrivacyContent />
              )}
            </div>
          </div>
        </CardContent>

        {showAcceptButton && (
          <div className="flex-shrink-0 border-t p-4 bg-slate-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-slate-600">
                {!hasScrolledToBottom && (
                  <>
                    <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                    Please scroll to the bottom to continue
                  </>
                )}
                {hasScrolledToBottom && (
                  <>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    You have reviewed the {title.toLowerCase()}
                  </>
                )}
              </div>
              <div className="flex gap-3">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleAccept}
                  disabled={!hasScrolledToBottom}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  Accept & Continue
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  )
}

function TermsContent() {
  return (
    <div className="space-y-6">
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <h3 className="font-bold text-orange-800 mb-2">Important Medical Disclaimer</h3>
        <p className="text-orange-700 text-sm leading-relaxed">
          Our health assessment and supplement recommendations are for informational purposes only and do not constitute medical advice. 
          Always consult with a qualified healthcare professional before starting any supplement regimen.
        </p>
      </div>

      <section>
        <h2 className="text-lg font-bold mb-3">1. Acceptance of Terms</h2>
        <p className="text-slate-700 leading-relaxed">
          By using LifeSupplier's health assessment service, you agree to these Terms of Service. 
          If you do not agree, you may not use our Service.
        </p>
      </section>

      <section>
        <h2 className="text-lg font-bold mb-3">2. Medical Disclaimer</h2>
        <ul className="list-disc list-inside text-slate-700 space-y-1 text-sm">
          <li><strong>NOT MEDICAL ADVICE:</strong> Our recommendations are educational only</li>
          <li><strong>CONSULT HEALTHCARE PROVIDERS:</strong> Always consult professionals</li>
          <li><strong>NO GUARANTEES:</strong> We make no claims about effectiveness</li>
          <li><strong>FDA DISCLAIMER:</strong> Statements not evaluated by the FDA</li>
        </ul>
      </section>

      <section>
        <h2 className="text-lg font-bold mb-3">3. User Responsibilities</h2>
        <p className="text-slate-700 leading-relaxed text-sm">
          You agree to provide accurate information, use the service responsibly, and take full responsibility for your health decisions.
        </p>
      </section>

      <section>
        <h2 className="text-lg font-bold mb-3">4. Limitation of Liability</h2>
        <p className="text-slate-700 leading-relaxed text-sm">
          LifeSupplier shall not be liable for any health complications, adverse reactions, or damages exceeding the amount paid for the service.
        </p>
      </section>

      <div className="text-xs text-slate-500 text-center pt-4 border-t">
        This is a summary. Please view the full Terms of Service for complete details.
      </div>
    </div>
  )
}

function PrivacyContent() {
  return (
    <div className="space-y-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="font-bold text-green-800 mb-2">Your Privacy Rights</h3>
        <div className="grid grid-cols-2 gap-2 text-xs text-green-700">
          <div>• You control your data</div>
          <div>• Bank-level encryption</div>
          <div>• No sale to third parties</div>
          <div>• GDPR & CCPA compliant</div>
        </div>
      </div>

      <section>
        <h2 className="text-lg font-bold mb-3">Information We Collect</h2>
        <ul className="list-disc list-inside text-slate-700 space-y-1 text-sm">
          <li>Health assessment responses</li>
          <li>Account information (email, name)</li>
          <li>Technical data (IP address, device info)</li>
        </ul>
      </section>

      <section>
        <h2 className="text-lg font-bold mb-3">How We Use Your Information</h2>
        <p className="text-slate-700 leading-relaxed text-sm">
          We use your data to generate personalized recommendations, maintain your account, and improve our service. 
          We never sell your information to third parties.
        </p>
      </section>

      <section>
        <h2 className="text-lg font-bold mb-3">Your Rights</h2>
        <ul className="list-disc list-inside text-slate-700 space-y-1 text-sm">
          <li>Access and download your data</li>
          <li>Update or correct information</li>
          <li>Delete your account anytime</li>
          <li>Opt out of marketing communications</li>
        </ul>
      </section>

      <section>
        <h2 className="text-lg font-bold mb-3">Data Security</h2>
        <p className="text-slate-700 leading-relaxed text-sm">
          We use AES-256 encryption, TLS 1.3 for data transmission, and follow industry best practices to protect your information.
        </p>
      </section>

      <div className="text-xs text-slate-500 text-center pt-4 border-t">
        This is a summary. Please view the full Privacy Policy for complete details.
      </div>
    </div>
  )
}