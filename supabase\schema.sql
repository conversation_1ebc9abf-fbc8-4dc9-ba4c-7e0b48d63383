-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE question_type AS ENUM ('multiple-choice', 'yes-no', 'scale');
CREATE TYPE recommendation_type AS ENUM ('supplement', 'food');
CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'past_due');
CREATE TYPE user_role AS ENUM ('user', 'admin');

-- Profiles table - extends auth.users with additional info
CREATE TABLE IF NOT EXISTS profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    role user_role DEFAULT 'user',
    newsletter_opt_in BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Questions table - stores all quiz questions
CREATE TABLE IF NOT EXISTS questions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    text TEXT NOT NULL,
    type question_type NOT NULL,
    options TEXT[] NOT NULL,
    "order" INTEGER NOT NULL,
    parent_id UUID REFERENCES questions(id),
    condition TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Responses table - stores user answers
CREATE TABLE IF NOT EXISTS responses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
    answer JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Health tags table - maps answers to health issues
CREATE TABLE IF NOT EXISTS health_tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tag_name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    triggers JSONB NOT NULL, -- Array of {question_id, answer} objects
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recommendations table - supplements and food recommendations
CREATE TABLE IF NOT EXISTS recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    health_tag_id UUID REFERENCES health_tags(id) ON DELETE CASCADE,
    type recommendation_type NOT NULL,
    name TEXT NOT NULL,
    details JSONB NOT NULL, -- {dosage, timing, benefits, description}
    affiliate_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reports table - stores generated PDF reports
CREATE TABLE IF NOT EXISTS reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    quiz_responses JSONB NOT NULL,
    health_tags TEXT[],
    recommendations JSONB NOT NULL,
    pdf_url TEXT,
    payment_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table - manages user subscriptions
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_id TEXT NOT NULL UNIQUE,
    status subscription_status NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_questions_order ON questions("order");
CREATE INDEX IF NOT EXISTS idx_questions_parent_id ON questions(parent_id);
CREATE INDEX IF NOT EXISTS idx_responses_user_id ON responses(user_id);
CREATE INDEX IF NOT EXISTS idx_responses_question_id ON responses(question_id);
CREATE INDEX IF NOT EXISTS idx_reports_user_id ON reports(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_id ON subscriptions(stripe_id);
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);

-- Row Level Security (RLS) policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON profiles 
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own profile" ON profiles 
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own profile" ON profiles 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Questions are public (readable by everyone)
CREATE POLICY "Questions are publicly readable" ON questions FOR SELECT USING (true);
-- Only admins can manage questions
CREATE POLICY "Admins can manage questions" ON questions 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Users can only read/write their own responses
CREATE POLICY "Users can manage their own responses" ON responses 
    FOR ALL USING (auth.uid() = user_id);

-- Health tags are public (readable by everyone)
CREATE POLICY "Health tags are publicly readable" ON health_tags FOR SELECT USING (true);
-- Only admins can manage health tags
CREATE POLICY "Admins can manage health tags" ON health_tags 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Recommendations are public (readable by everyone)
CREATE POLICY "Recommendations are publicly readable" ON recommendations FOR SELECT USING (true);
-- Only admins can manage recommendations
CREATE POLICY "Admins can manage recommendations" ON recommendations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Users can only read their own reports
CREATE POLICY "Users can read their own reports" ON reports 
    FOR SELECT USING (auth.uid() = user_id);

-- System can insert reports (for payment processing)
CREATE POLICY "System can insert reports" ON reports 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only read their own subscriptions
CREATE POLICY "Users can read their own subscriptions" ON subscriptions 
    FOR SELECT USING (auth.uid() = user_id);

-- System can manage subscriptions (for Stripe webhooks)
CREATE POLICY "System can manage subscriptions" ON subscriptions 
    FOR ALL WITH CHECK (true);

-- Function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (user_id, full_name)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at on profiles
CREATE OR REPLACE TRIGGER on_profiles_updated
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();