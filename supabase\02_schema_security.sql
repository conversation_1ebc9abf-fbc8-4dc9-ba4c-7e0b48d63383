-- ============================================================================
-- Row Level Security (RLS) Policies - Run this second
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- Profiles Policies
-- ============================================================================

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;

CREATE POLICY "Users can view their own profile" ON profiles 
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own profile" ON profiles 
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own profile" ON profiles 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ============================================================================
-- Questions Policies
-- ============================================================================

DROP POLICY IF EXISTS "Questions are publicly readable" ON questions;
DROP POLICY IF EXISTS "Admins can manage questions" ON questions;

-- Questions are public (readable by everyone)
CREATE POLICY "Questions are publicly readable" ON questions FOR SELECT USING (true);

-- Only admins can manage questions
CREATE POLICY "Admins can manage questions" ON questions 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- ============================================================================
-- Responses Policies
-- ============================================================================

DROP POLICY IF EXISTS "Users can manage their own responses" ON responses;

-- Users can only read/write their own responses
CREATE POLICY "Users can manage their own responses" ON responses 
    FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- Health Tags Policies
-- ============================================================================

DROP POLICY IF EXISTS "Health tags are publicly readable" ON health_tags;
DROP POLICY IF EXISTS "Admins can manage health tags" ON health_tags;

-- Health tags are public (readable by everyone)
CREATE POLICY "Health tags are publicly readable" ON health_tags FOR SELECT USING (true);

-- Only admins can manage health tags
CREATE POLICY "Admins can manage health tags" ON health_tags 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- ============================================================================
-- Recommendations Policies
-- ============================================================================

DROP POLICY IF EXISTS "Recommendations are publicly readable" ON recommendations;
DROP POLICY IF EXISTS "Admins can manage recommendations" ON recommendations;

-- Recommendations are public (readable by everyone)
CREATE POLICY "Recommendations are publicly readable" ON recommendations FOR SELECT USING (true);

-- Only admins can manage recommendations
CREATE POLICY "Admins can manage recommendations" ON recommendations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- ============================================================================
-- Reports Policies
-- ============================================================================

DROP POLICY IF EXISTS "Users can read their own reports" ON reports;
DROP POLICY IF EXISTS "System can insert reports" ON reports;

-- Users can only read their own reports
CREATE POLICY "Users can read their own reports" ON reports 
    FOR SELECT USING (auth.uid() = user_id);

-- System can insert reports (for payment processing)
CREATE POLICY "System can insert reports" ON reports 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ============================================================================
-- Subscriptions Policies
-- ============================================================================

DROP POLICY IF EXISTS "Users can read their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "System can manage subscriptions" ON subscriptions;

-- Users can only read their own subscriptions
CREATE POLICY "Users can read their own subscriptions" ON subscriptions 
    FOR SELECT USING (auth.uid() = user_id);

-- System can manage subscriptions (for Stripe webhooks)
CREATE POLICY "System can manage subscriptions" ON subscriptions 
    FOR ALL WITH CHECK (true);