export interface Database {
  public: {
    Tables: {
      questions: {
        Row: {
          id: string
          text: string
          type: 'multiple-choice' | 'yes-no' | 'scale'
          options: string[]
          order: number
          parent_id: string | null
          condition: string | null
          created_at: string
        }
        Insert: {
          id?: string
          text: string
          type: 'multiple-choice' | 'yes-no' | 'scale'
          options: string[]
          order: number
          parent_id?: string | null
          condition?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          text?: string
          type?: 'multiple-choice' | 'yes-no' | 'scale'
          options?: string[]
          order?: number
          parent_id?: string | null
          condition?: string | null
          created_at?: string
        }
      }
      responses: {
        Row: {
          id: string
          user_id: string
          question_id: string
          answer: any
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          question_id: string
          answer: any
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          question_id?: string
          answer?: any
          created_at?: string
        }
      }
      health_tags: {
        Row: {
          id: string
          tag_name: string
          description: string
          triggers: Array<{
            question_id: string
            answer: string
          }>
          created_at: string
        }
        Insert: {
          id?: string
          tag_name: string
          description: string
          triggers: Array<{
            question_id: string
            answer: string
          }>
          created_at?: string
        }
        Update: {
          id?: string
          tag_name?: string
          description?: string
          triggers?: Array<{
            question_id: string
            answer: string
          }>
          created_at?: string
        }
      }
      recommendations: {
        Row: {
          id: string
          health_tag_id: string
          type: 'supplement' | 'food'
          name: string
          details: {
            dosage?: string
            timing?: string
            benefits: string[]
            description: string
          }
          affiliate_link: string | null
          created_at: string
        }
        Insert: {
          id?: string
          health_tag_id: string
          type: 'supplement' | 'food'
          name: string
          details: {
            dosage?: string
            timing?: string
            benefits: string[]
            description: string
          }
          affiliate_link?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          health_tag_id?: string
          type?: 'supplement' | 'food'
          name?: string
          details?: {
            dosage?: string
            timing?: string
            benefits: string[]
            description: string
          }
          affiliate_link?: string | null
          created_at?: string
        }
      }
      reports: {
        Row: {
          id: string
          user_id: string
          quiz_responses: Record<string, any>
          health_tags: string[]
          recommendations: any[]
          pdf_url: string | null
          payment_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          quiz_responses: Record<string, any>
          health_tags: string[]
          recommendations: any[]
          pdf_url?: string | null
          payment_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          quiz_responses?: Record<string, any>
          health_tags?: string[]
          recommendations?: any[]
          pdf_url?: string | null
          payment_id?: string
          created_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_id: string
          status: 'active' | 'canceled' | 'past_due'
          price: number
          period_end: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_id: string
          status: 'active' | 'canceled' | 'past_due'
          price: number
          period_end: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_id?: string
          status?: 'active' | 'canceled' | 'past_due'
          price?: number
          period_end?: string
          created_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          user_id: string
          full_name: string | null
          avatar_url: string | null
          role: 'user' | 'admin'
          newsletter_opt_in: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'admin'
          newsletter_opt_in?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'admin'
          newsletter_opt_in?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}