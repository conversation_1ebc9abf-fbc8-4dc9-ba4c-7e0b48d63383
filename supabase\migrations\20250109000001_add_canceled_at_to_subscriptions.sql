-- Add canceled_at field to subscriptions table for tracking cancellation timestamp
-- This field is used by the cancel-subscription function and grace period logic

ALTER TABLE subscriptions 
ADD COLUMN IF NOT EXISTS canceled_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- Add index for better performance when querying by cancellation status
CREATE INDEX IF NOT EXISTS idx_subscriptions_canceled_at ON subscriptions(canceled_at);